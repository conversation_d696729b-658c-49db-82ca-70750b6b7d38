//! Error recovery mechanisms for WhatsApp client operations

use crate::error::{Result, WhatsAppError};
use std::time::Duration;
use tokio::time::sleep;

/// Retry configuration for operations
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            jitter: true,
        }
    }
}

impl RetryConfig {
    /// Create a new retry configuration
    pub fn new() -> Self {
        Self::default()
    }

    /// Set maximum number of retry attempts
    pub fn max_attempts(mut self, attempts: u32) -> Self {
        self.max_attempts = attempts;
        self
    }

    /// Set base delay between retries
    pub fn base_delay(mut self, delay: Duration) -> Self {
        self.base_delay = delay;
        self
    }

    /// Set maximum delay between retries
    pub fn max_delay(mut self, delay: Duration) -> Self {
        self.max_delay = delay;
        self
    }

    /// Set backoff multiplier for exponential backoff
    pub fn backoff_multiplier(mut self, multiplier: f64) -> Self {
        self.backoff_multiplier = multiplier;
        self
    }

    /// Enable or disable jitter to prevent thundering herd
    pub fn jitter(mut self, enable: bool) -> Self {
        self.jitter = enable;
        self
    }

    /// Calculate delay for a given attempt
    pub fn delay_for_attempt(&self, attempt: u32) -> Duration {
        let base_delay_ms = self.base_delay.as_millis() as f64;
        let delay_ms = base_delay_ms * self.backoff_multiplier.powi(attempt as i32);
        let delay_ms = delay_ms.min(self.max_delay.as_millis() as f64);

        let mut delay = Duration::from_millis(delay_ms as u64);

        // Add jitter to prevent thundering herd
        if self.jitter {
            use rand::Rng;
            let jitter_factor = rand::rng().random::<f64>() * 0.1; // ±10% jitter
            let jitter_ms = (delay.as_millis() as f64 * jitter_factor) as u64;
            delay = Duration::from_millis(delay.as_millis() as u64 + jitter_ms);
        }

        delay
    }
}

/// Recovery strategy for different types of errors
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// Retry the operation with exponential backoff
    Retry(RetryConfig),
    /// Reconnect and then retry
    ReconnectAndRetry(RetryConfig),
    /// Clear session and re-authenticate
    Reauthenticate,
    /// Fail immediately without retry
    Fail,
}

impl RecoveryStrategy {
    /// Get recovery strategy for a specific error
    pub fn for_error(error: &WhatsAppError) -> Self {
        match error {
            WhatsAppError::Connection(_) | WhatsAppError::Network(_) => {
                Self::ReconnectAndRetry(RetryConfig::new().max_attempts(5))
            }
            WhatsAppError::NotConnected => {
                Self::ReconnectAndRetry(RetryConfig::new().max_attempts(3))
            }
            WhatsAppError::Timeout { .. } => Self::Retry(
                RetryConfig::new()
                    .max_attempts(3)
                    .base_delay(Duration::from_secs(1)),
            ),
            WhatsAppError::Transient { .. } => Self::Retry(RetryConfig::new().max_attempts(5)),
            WhatsAppError::RateLimited { .. } => Self::Retry(
                RetryConfig::new()
                    .max_attempts(3)
                    .base_delay(Duration::from_secs(5)),
            ),
            WhatsAppError::Authentication(_) => Self::Reauthenticate,
            WhatsAppError::Session(_) => Self::Reauthenticate,
            WhatsAppError::InvalidInput(_) | WhatsAppError::InvalidConfig(_) => Self::Fail,
            _ => Self::Retry(RetryConfig::new().max_attempts(2)),
        }
    }
}

/// Error recovery manager
pub struct ErrorRecovery;

impl ErrorRecovery {
    /// Execute an operation with automatic retry and recovery
    pub async fn with_retry<F, Fut, T>(
        operation: F,
        config: RetryConfig,
        operation_name: &str,
    ) -> Result<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        let mut last_error = None;

        for attempt in 0..config.max_attempts {
            match operation().await {
                Ok(result) => {
                    if attempt > 0 {
                        tracing::info!(
                            "Operation '{}' succeeded after {} attempts",
                            operation_name,
                            attempt + 1
                        );
                    }
                    return Ok(result);
                }
                Err(error) => {
                    error.log_with_context(&format!(
                        "Attempt {} of {}",
                        attempt + 1,
                        operation_name
                    ));

                    // Check if we should retry this error
                    if !error.is_recoverable() {
                        tracing::error!(
                            "Operation '{}' failed with non-recoverable error: {}",
                            operation_name,
                            error
                        );
                        return Err(error);
                    }

                    last_error = Some(error);

                    // Don't sleep after the last attempt
                    if attempt < config.max_attempts - 1 {
                        let delay = config.delay_for_attempt(attempt);
                        tracing::debug!(
                            "Retrying operation '{}' in {:?} (attempt {}/{})",
                            operation_name,
                            delay,
                            attempt + 1,
                            config.max_attempts
                        );
                        sleep(delay).await;
                    }
                }
            }
        }

        // All attempts failed
        let final_error = last_error
            .unwrap_or_else(|| WhatsAppError::Generic("All retry attempts failed".to_string()));

        tracing::error!(
            "Operation '{}' failed after {} attempts: {}",
            operation_name,
            config.max_attempts,
            final_error
        );

        Err(WhatsAppError::transient(
            format!("Operation '{}' failed after retries", operation_name),
            config.max_attempts,
            config.max_attempts,
            Some(final_error),
        ))
    }

    /// Execute an operation with rate limit handling
    pub async fn with_rate_limit_handling<F, Fut, T>(
        operation: F,
        operation_name: &str,
    ) -> Result<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        loop {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(WhatsAppError::RateLimited { retry_after, .. }) => {
                    let delay = retry_after.unwrap_or(Duration::from_secs(5));
                    tracing::warn!(
                        "Operation '{}' rate limited, waiting {:?}",
                        operation_name,
                        delay
                    );
                    sleep(delay).await;
                    continue;
                }
                Err(error) => return Err(error),
            }
        }
    }

    /// Execute an operation with comprehensive error handling
    pub async fn execute_with_recovery<F, Fut, T>(operation: F, operation_name: &str) -> Result<T>
    where
        F: Fn() -> Fut + Clone,
        Fut: std::future::Future<Output = Result<T>>,
    {
        // First attempt
        match operation().await {
            Ok(result) => return Ok(result),
            Err(error) => {
                let strategy = RecoveryStrategy::for_error(&error);

                match strategy {
                    RecoveryStrategy::Retry(config) => {
                        Self::with_retry(operation, config, operation_name).await
                    }
                    RecoveryStrategy::ReconnectAndRetry(config) => {
                        tracing::info!(
                            "Attempting reconnection for operation '{}'",
                            operation_name
                        );
                        // Note: In a real implementation, this would trigger reconnection
                        // For now, we just retry with the config
                        Self::with_retry(operation, config, operation_name).await
                    }
                    RecoveryStrategy::Reauthenticate => {
                        tracing::warn!(
                            "Re-authentication required for operation '{}'",
                            operation_name
                        );
                        Err(WhatsAppError::Authentication(
                            "Re-authentication required".to_string(),
                        ))
                    }
                    RecoveryStrategy::Fail => {
                        tracing::error!(
                            "Operation '{}' failed without recovery: {}",
                            operation_name,
                            error
                        );
                        Err(error)
                    }
                }
            }
        }
    }
}

/// Circuit breaker for preventing cascading failures
#[derive(Debug)]
pub struct CircuitBreaker {
    failure_threshold: u32,
    recovery_timeout: Duration,
    failure_count: std::sync::atomic::AtomicU32,
    last_failure_time: std::sync::Mutex<Option<std::time::Instant>>,
    state: std::sync::Mutex<CircuitBreakerState>,
}

#[derive(Debug, Clone, PartialEq)]
enum CircuitBreakerState {
    Closed,   // Normal operation
    Open,     // Failing fast
    HalfOpen, // Testing if service recovered
}

impl CircuitBreaker {
    /// Create a new circuit breaker
    pub fn new(failure_threshold: u32, recovery_timeout: Duration) -> Self {
        Self {
            failure_threshold,
            recovery_timeout,
            failure_count: std::sync::atomic::AtomicU32::new(0),
            last_failure_time: std::sync::Mutex::new(None),
            state: std::sync::Mutex::new(CircuitBreakerState::Closed),
        }
    }

    /// Execute an operation through the circuit breaker
    pub async fn execute<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        // Check if circuit is open
        if self.is_open().await {
            return Err(WhatsAppError::Generic(
                "Circuit breaker is open - failing fast".to_string(),
            ));
        }

        match operation().await {
            Ok(result) => {
                self.on_success().await;
                Ok(result)
            }
            Err(error) => {
                self.on_failure().await;
                Err(error)
            }
        }
    }

    async fn is_open(&self) -> bool {
        let state = self.state.lock().unwrap();
        match *state {
            CircuitBreakerState::Open => {
                // Check if we should transition to half-open
                if let Some(last_failure) = *self.last_failure_time.lock().unwrap() {
                    if last_failure.elapsed() > self.recovery_timeout {
                        drop(state);
                        self.transition_to_half_open().await;
                        false
                    } else {
                        true
                    }
                } else {
                    true
                }
            }
            _ => false,
        }
    }

    async fn on_success(&self) {
        self.failure_count
            .store(0, std::sync::atomic::Ordering::Relaxed);
        let mut state = self.state.lock().unwrap();
        *state = CircuitBreakerState::Closed;
    }

    async fn on_failure(&self) {
        let failures = self
            .failure_count
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed)
            + 1;
        *self.last_failure_time.lock().unwrap() = Some(std::time::Instant::now());

        if failures >= self.failure_threshold {
            let mut state = self.state.lock().unwrap();
            *state = CircuitBreakerState::Open;
            tracing::warn!("Circuit breaker opened after {} failures", failures);
        }
    }

    async fn transition_to_half_open(&self) {
        let mut state = self.state.lock().unwrap();
        *state = CircuitBreakerState::HalfOpen;
        tracing::info!("Circuit breaker transitioned to half-open");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use std::sync::atomic::{AtomicU32, Ordering};

    #[tokio::test]
    async fn test_retry_config() {
        let config = RetryConfig::new()
            .max_attempts(3)
            .base_delay(Duration::from_millis(100))
            .backoff_multiplier(2.0);

        assert_eq!(config.max_attempts, 3);
        assert!(config.delay_for_attempt(0) >= Duration::from_millis(100));
        assert!(config.delay_for_attempt(1) >= Duration::from_millis(200));
    }

    #[tokio::test]
    async fn test_error_recovery_success() {
        let counter = Arc::new(AtomicU32::new(0));
        let counter_clone = counter.clone();

        let result = ErrorRecovery::with_retry(
            || {
                let counter = counter_clone.clone();
                async move {
                    let count = counter.fetch_add(1, Ordering::Relaxed);
                    if count < 2 {
                        Err(WhatsAppError::Network("Temporary failure".to_string()))
                    } else {
                        Ok("Success")
                    }
                }
            },
            RetryConfig::new().max_attempts(5),
            "test_operation",
        )
        .await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Success");
        assert_eq!(counter.load(Ordering::Relaxed), 3);
    }

    #[tokio::test]
    async fn test_error_recovery_failure() {
        let result: Result<&str> = ErrorRecovery::with_retry(
            || async { Err(WhatsAppError::Network("Persistent failure".to_string())) },
            RetryConfig::new().max_attempts(2),
            "test_operation",
        )
        .await;

        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            WhatsAppError::Transient { .. }
        ));
    }

    #[tokio::test]
    async fn test_circuit_breaker() {
        let breaker = CircuitBreaker::new(2, Duration::from_millis(100));

        // First failure
        let result1 = breaker
            .execute(|| async { Err::<(), _>(WhatsAppError::Network("Failure 1".to_string())) })
            .await;
        assert!(result1.is_err());

        // Second failure - should open circuit
        let result2 = breaker
            .execute(|| async { Err::<(), _>(WhatsAppError::Network("Failure 2".to_string())) })
            .await;
        assert!(result2.is_err());

        // Third attempt - should fail fast
        let result3 = breaker
            .execute(|| async { Ok::<(), WhatsAppError>(()) })
            .await;
        assert!(result3.is_err());
        assert!(
            result3
                .unwrap_err()
                .to_string()
                .contains("Circuit breaker is open")
        );
    }
}
