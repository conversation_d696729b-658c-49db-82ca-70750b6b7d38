use crate::error::{Result, WhatsAppError};
/// WhatsApp Web binary protocol implementation
///
/// This module implements the binary protocol used by WhatsApp Web for communication
/// with WhatsApp servers, following the same patterns as the whatsmeow Go library.
use std::collections::HashMap;

/// Represents a binary node in the WhatsApp Web protocol
#[derive(Debu<PERSON>, Clone)]
pub struct BinaryNode {
    pub tag: String,
    pub attributes: HashMap<String, String>,
    pub content: Option<Vec<BinaryNode>>,
}

impl BinaryNode {
    /// Create a new binary node
    pub fn new(tag: String) -> Self {
        Self {
            tag,
            attributes: HashMap::new(),
            content: None,
        }
    }

    /// Add an attribute to the node
    pub fn with_attribute(mut self, key: String, value: String) -> Self {
        self.attributes.insert(key, value);
        self
    }

    /// Add child nodes to the node
    pub fn with_children(mut self, children: Vec<BinaryNode>) -> Self {
        self.content = Some(children);
        self
    }
}

/// Binary encoder for WhatsApp Web protocol
pub struct BinaryEncoder {
    buffer: Vec<u8>,
}

impl BinaryEncoder {
    pub fn new() -> Self {
        Self { buffer: Vec::new() }
    }

    /// Encode a binary node to bytes following WhatsApp Web protocol
    pub fn encode(&mut self, node: &BinaryNode) -> Result<Vec<u8>> {
        self.buffer.clear();

        // WhatsApp Web binary protocol header: "WA" + version + dict version
        self.buffer.extend_from_slice(&[0x57, 0x41, 0x06, 0x01]); // "WA" + magic value + dict version

        // Encode the node structure using WhatsApp's binary format
        self.encode_node(node)?;

        // Add frame length prefix (3 bytes) as per WhatsApp Web protocol
        let payload = self.buffer[4..].to_vec(); // Skip header for length calculation
        let length = payload.len();

        // Create final frame with length prefix
        let mut final_frame = Vec::new();
        final_frame.extend_from_slice(&[0x57, 0x41, 0x06, 0x01]); // Header
        final_frame.push((length >> 16) as u8); // Length high byte
        final_frame.push((length >> 8) as u8); // Length middle byte
        final_frame.push(length as u8); // Length low byte
        final_frame.extend_from_slice(&payload);

        Ok(final_frame)
    }

    fn encode_node(&mut self, node: &BinaryNode) -> Result<()> {
        // Encode tag
        self.encode_string(&node.tag)?;

        // Encode attributes count
        self.encode_varint(node.attributes.len() as u64)?;

        // Encode attributes
        for (key, value) in &node.attributes {
            self.encode_string(key)?;
            self.encode_string(value)?;
        }

        // Encode children
        if let Some(children) = &node.content {
            self.encode_varint(children.len() as u64)?;
            for child in children {
                self.encode_node(child)?;
            }
        } else {
            self.encode_varint(0)?;
        }

        Ok(())
    }

    fn encode_string(&mut self, s: &str) -> Result<()> {
        let bytes = s.as_bytes();
        self.encode_varint(bytes.len() as u64)?;
        self.buffer.extend_from_slice(bytes);
        Ok(())
    }

    fn encode_varint(&mut self, mut value: u64) -> Result<()> {
        while value >= 0x80 {
            self.buffer.push((value & 0x7F) as u8 | 0x80);
            value >>= 7;
        }
        self.buffer.push(value as u8);
        Ok(())
    }
}

/// Binary decoder for WhatsApp Web protocol
pub struct BinaryDecoder {
    position: usize,
}

impl BinaryDecoder {
    pub fn new() -> Self {
        Self { position: 0 }
    }

    /// Decode bytes to a binary node following WhatsApp Web protocol
    pub fn decode(&mut self, data: &[u8]) -> Result<BinaryNode> {
        self.position = 0;

        // Check minimum frame size (header + length + minimal payload)
        if data.len() < 7 {
            return Err(WhatsAppError::Protocol(
                "Invalid binary data: frame too short".to_string(),
            ));
        }

        // Check for WA header
        if &data[0..2] != b"WA" {
            return Err(WhatsAppError::Protocol(
                "Invalid binary data: missing WA header".to_string(),
            ));
        }

        // Skip header (4 bytes: "WA" + magic + dict version)
        self.position = 4;

        // Read frame length (3 bytes)
        let length = ((data[4] as usize) << 16) | ((data[5] as usize) << 8) | (data[6] as usize);

        // Verify frame length matches remaining data
        if data.len() < 7 + length {
            return Err(WhatsAppError::Protocol(
                "Invalid binary data: frame length mismatch".to_string(),
            ));
        }

        // Skip length bytes and decode the payload
        self.position = 7;

        self.decode_node(data)
    }

    fn decode_node(&mut self, data: &[u8]) -> Result<BinaryNode> {
        // Decode tag
        let tag = self.decode_string(data)?;

        // Decode attributes count
        let attr_count = self.decode_varint(data)? as usize;

        // Decode attributes
        let mut attributes = HashMap::new();
        for _ in 0..attr_count {
            let key = self.decode_string(data)?;
            let value = self.decode_string(data)?;
            attributes.insert(key, value);
        }

        // Decode children count
        let children_count = self.decode_varint(data)? as usize;

        // Decode children
        let content = if children_count > 0 {
            let mut children = Vec::new();
            for _ in 0..children_count {
                children.push(self.decode_node(data)?);
            }
            Some(children)
        } else {
            None
        };

        Ok(BinaryNode {
            tag,
            attributes,
            content,
        })
    }

    fn decode_string(&mut self, data: &[u8]) -> Result<String> {
        let length = self.decode_varint(data)? as usize;

        if self.position + length > data.len() {
            return Err(WhatsAppError::Protocol(
                "Invalid binary data: string length exceeds data".to_string(),
            ));
        }

        let string_bytes = &data[self.position..self.position + length];
        self.position += length;

        String::from_utf8(string_bytes.to_vec())
            .map_err(|e| WhatsAppError::Protocol(format!("Invalid UTF-8 in binary data: {}", e)))
    }

    fn decode_varint(&mut self, data: &[u8]) -> Result<u64> {
        let mut result = 0u64;
        let mut shift = 0;

        loop {
            if self.position >= data.len() {
                return Err(WhatsAppError::Protocol(
                    "Invalid binary data: incomplete varint".to_string(),
                ));
            }

            let byte = data[self.position];
            self.position += 1;

            result |= ((byte & 0x7F) as u64) << shift;

            if byte & 0x80 == 0 {
                break;
            }

            shift += 7;
            if shift >= 64 {
                return Err(WhatsAppError::Protocol(
                    "Invalid binary data: varint too long".to_string(),
                ));
            }
        }

        Ok(result)
    }
}

impl Default for BinaryEncoder {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for BinaryDecoder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encode_decode_simple_node() {
        let node =
            BinaryNode::new("test".to_string()).with_attribute("id".to_string(), "123".to_string());

        let mut encoder = BinaryEncoder::new();
        let encoded = encoder.encode(&node).unwrap();

        let mut decoder = BinaryDecoder::new();
        let decoded = decoder.decode(&encoded).unwrap();

        assert_eq!(decoded.tag, "test");
        assert_eq!(decoded.attributes.get("id"), Some(&"123".to_string()));
    }

    #[test]
    fn test_encode_decode_with_children() {
        let child = BinaryNode::new("child".to_string());
        let parent = BinaryNode::new("parent".to_string()).with_children(vec![child]);

        let mut encoder = BinaryEncoder::new();
        let encoded = encoder.encode(&parent).unwrap();

        let mut decoder = BinaryDecoder::new();
        let decoded = decoder.decode(&encoded).unwrap();

        assert_eq!(decoded.tag, "parent");
        assert!(decoded.content.is_some());
        assert_eq!(decoded.content.as_ref().unwrap().len(), 1);
        assert_eq!(decoded.content.as_ref().unwrap()[0].tag, "child");
    }
}
