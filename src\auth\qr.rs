//! QR code generation and handling

use crate::error::{Result, WhatsAppError};
use base64::{Engine, engine::general_purpose};
use rand::RngCore;
use std::io::{self, Write};

use colored::*;
use qrcode::QrCode;
use qrcode::render::unicode;
use tracing::error;

/// QR code generator for WhatsApp pairing
pub struct QRGenerator {
    pub ref_id: String,
    pub public_key: Vec<u8>,
    pub private_key: Vec<u8>,
}

impl QRGenerator {
    /// Create a new QR generator with random keys and reference ID
    pub fn new() -> Self {
        let mut rng = rand::rng();

        // Generate a random reference ID (16 bytes, base64 encoded)
        let mut ref_id_bytes = [0u8; 16];
        rng.fill_bytes(&mut ref_id_bytes);
        let ref_id = general_purpose::STANDARD.encode(ref_id_bytes);

        // Generate key pair (32 bytes each for Curve25519)
        let mut public_key = vec![0u8; 32];
        let mut private_key = vec![0u8; 32];
        rng.fill_bytes(&mut public_key);
        rng.fill_bytes(&mut private_key);

        Self {
            ref_id,
            public_key,
            private_key,
        }
    }

    /// Generate QR code data string
    /// Format follows WhatsApp Web protocol: ref_id,public_key_base64,1
    pub fn generate_qr_data(&self) -> String {
        let public_key_b64 = general_purpose::STANDARD.encode(&self.public_key);
        format!("{},{},1", self.ref_id, public_key_b64)
    }

    /// Display QR code in terminal
    pub fn display_qr(&self) -> Result<()> {
        let qr_data = self.generate_qr_data();

        match QrCode::new(qr_data.as_bytes()) {
            Ok(code) => {
                // Render the QR code with Unicode characters
                let qr_string = code
                    .render::<unicode::Dense1x2>()
                    .dark_color(unicode::Dense1x2::Light)
                    .light_color(unicode::Dense1x2::Dark)
                    .build();

                // Print QR code with a border
                let lines: Vec<&str> = qr_string.lines().collect();
                let width = lines[0].chars().count();

                // Calculate padding for centering the title
                let title = "Scan this QR code with WhatsApp to log in";
                let total_width = width + 4;
                let padding = if total_width > title.len() {
                    (total_width - title.len()) / 2
                } else {
                    0
                };
                let title_line = " ".repeat(padding) + title;

                eprintln!("\n{}", title_line.bright_cyan().bold());
                eprintln!();

                // Top border with rounded corners
                eprint!("  {}", "╭".bright_blue());
                for _ in 0..width + 2 {
                    eprint!("{}", "─".bright_blue());
                }
                eprintln!("{}", "╮".bright_blue());

                // Print QR code with side borders
                for line in lines {
                    eprintln!("  {} {} {}", "│".bright_blue(), line, "│".bright_blue());
                }

                // Bottom border with rounded corners
                eprint!("  {}", "╰".bright_blue());
                for _ in 0..width + 2 {
                    eprint!("{}", "─".bright_blue());
                }
                eprintln!("{}", "╯".bright_blue());

                eprintln!(
                    "\n  {} {}\n",
                    "• Ready to connect!".bright_blue().bold(),
                    "Waiting for scan...".normal()
                );
            }
            Err(e) => {
                error!("Failed to generate QR code: {}", e);
                eprintln!("Raw QR content: {}", qr_data);
            }
        }

        // Flush stdout to ensure immediate display
        io::stdout().flush().map_err(WhatsAppError::Io)?;

        Ok(())
    }
}

impl Default for QRGenerator {
    fn default() -> Self {
        Self::new()
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_qr_generator_new() {
        let qr_gen = QRGenerator::new();

        // Check that fields are populated
        assert!(!qr_gen.ref_id.is_empty());
        assert_eq!(qr_gen.public_key.len(), 32);
        assert_eq!(qr_gen.private_key.len(), 32);

        // Check that ref_id is valid base64
        assert!(general_purpose::STANDARD.decode(&qr_gen.ref_id).is_ok());
    }

    #[test]
    fn test_qr_generator_uniqueness() {
        let qr_gen1 = QRGenerator::new();
        let qr_gen2 = QRGenerator::new();

        // Each instance should have unique values
        assert_ne!(qr_gen1.ref_id, qr_gen2.ref_id);
        assert_ne!(qr_gen1.public_key, qr_gen2.public_key);
        assert_ne!(qr_gen1.private_key, qr_gen2.private_key);
    }

    #[test]
    fn test_generate_qr_data_format() {
        let qr_gen = QRGenerator::new();
        let qr_data = qr_gen.generate_qr_data();

        // Check format: ref_id,public_key_base64,1
        let parts: Vec<&str> = qr_data.split(',').collect();
        assert_eq!(parts.len(), 3);
        assert_eq!(parts[0], qr_gen.ref_id);
        assert_eq!(parts[2], "1");

        // Verify public key part is valid base64
        let decoded_key = general_purpose::STANDARD.decode(parts[1]).unwrap();
        assert_eq!(decoded_key, qr_gen.public_key);
    }

    #[test]
    fn test_generate_qr_data_consistency() {
        let qr_gen = QRGenerator::new();
        let qr_data1 = qr_gen.generate_qr_data();
        let qr_data2 = qr_gen.generate_qr_data();

        // Same instance should generate same QR data
        assert_eq!(qr_data1, qr_data2);
    }

    #[test]
    fn test_display_qr_no_panic() {
        let qr_gen = QRGenerator::new();

        // This should not panic - we can't easily test the output
        // but we can ensure it doesn't crash
        let result = qr_gen.display_qr();
        assert!(result.is_ok());
    }

    #[test]
    fn test_default_implementation() {
        let qr_gen = QRGenerator::default();

        // Default should work the same as new()
        assert!(!qr_gen.ref_id.is_empty());
        assert_eq!(qr_gen.public_key.len(), 32);
        assert_eq!(qr_gen.private_key.len(), 32);
    }

    #[test]
    fn test_qr_data_can_be_encoded_as_qr() {
        let qr_gen = QRGenerator::new();
        let qr_data = qr_gen.generate_qr_data();

        // Verify the data can actually be encoded as a QR code
        let result = QrCode::new(&qr_data);
        assert!(result.is_ok());
    }
}
