//! Logging utilities for WhatsApp client operations

use crate::error::WhatsAppError;
use std::time::{Duration, Instant};

/// Logging helper for client operations
pub struct OperationLogger {
    operation_name: String,
    start_time: Instant,
}

impl OperationLogger {
    /// Start logging an operation
    pub fn start(operation_name: impl Into<String>) -> Self {
        let operation_name = operation_name.into();
        tracing::debug!("Starting operation: {}", operation_name);

        Self {
            operation_name,
            start_time: Instant::now(),
        }
    }

    /// Log successful completion of the operation
    pub fn success(self) {
        let duration = self.start_time.elapsed();
        tracing::info!(
            "Operation '{}' completed successfully in {:?}",
            self.operation_name,
            duration
        );
    }

    /// Log successful completion with additional context
    pub fn success_with_context(self, context: &str) {
        let duration = self.start_time.elapsed();
        tracing::info!(
            "Operation '{}' completed successfully in {:?}: {}",
            self.operation_name,
            duration,
            context
        );
    }

    /// Log operation failure
    pub fn failure(self, error: &WhatsAppError) {
        let duration = self.start_time.elapsed();
        error.log_with_context(&format!(
            "Operation '{}' failed after {:?}",
            self.operation_name, duration
        ));
    }

    /// Log operation failure with additional context
    pub fn failure_with_context(self, error: &WhatsAppError, context: &str) {
        let duration = self.start_time.elapsed();
        error.log_with_context(&format!(
            "Operation '{}' failed after {:?}: {}",
            self.operation_name, duration, context
        ));
    }

    /// Get the operation name
    pub fn operation_name(&self) -> &str {
        &self.operation_name
    }

    /// Get elapsed time since operation start
    pub fn elapsed(&self) -> Duration {
        self.start_time.elapsed()
    }
}

/// Macro for logging operation results
#[macro_export]
macro_rules! log_operation {
    ($operation:expr, $result:expr) => {{
        let logger = $crate::client::logging::OperationLogger::start($operation);
        match &$result {
            Ok(_) => logger.success(),
            Err(e) => logger.failure(e),
        }
        $result
    }};

    ($operation:expr, $result:expr, $context:expr) => {{
        let logger = $crate::client::logging::OperationLogger::start($operation);
        match &$result {
            Ok(_) => logger.success_with_context($context),
            Err(e) => logger.failure_with_context(e, $context),
        }
        $result
    }};
}

/// Performance monitoring for operations
pub struct PerformanceMonitor {
    operation_counts: std::sync::Mutex<std::collections::HashMap<String, u64>>,
    operation_durations: std::sync::Mutex<std::collections::HashMap<String, Vec<Duration>>>,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new() -> Self {
        Self {
            operation_counts: std::sync::Mutex::new(std::collections::HashMap::new()),
            operation_durations: std::sync::Mutex::new(std::collections::HashMap::new()),
        }
    }

    /// Record an operation completion
    pub fn record_operation(&self, operation: &str, duration: Duration, success: bool) {
        // Update counts
        {
            let mut counts = self.operation_counts.lock().unwrap();
            *counts.entry(operation.to_string()).or_insert(0) += 1;
        }

        // Update durations for successful operations
        if success {
            let mut durations = self.operation_durations.lock().unwrap();
            durations
                .entry(operation.to_string())
                .or_insert_with(Vec::new)
                .push(duration);
        }

        // Log performance metrics periodically
        if let Ok(counts) = self.operation_counts.lock() {
            if let Some(&count) = counts.get(operation) {
                if count % 100 == 0 {
                    self.log_performance_summary(operation);
                }
            }
        }
    }

    /// Log performance summary for an operation
    fn log_performance_summary(&self, operation: &str) {
        let counts = self.operation_counts.lock().unwrap();
        let durations = self.operation_durations.lock().unwrap();

        if let (Some(&count), Some(duration_list)) =
            (counts.get(operation), durations.get(operation))
        {
            if !duration_list.is_empty() {
                let total_duration: Duration = duration_list.iter().sum();
                let avg_duration = total_duration / duration_list.len() as u32;
                let min_duration = *duration_list.iter().min().unwrap();
                let max_duration = *duration_list.iter().max().unwrap();

                tracing::info!(
                    "Performance summary for '{}': {} operations, avg: {:?}, min: {:?}, max: {:?}",
                    operation,
                    count,
                    avg_duration,
                    min_duration,
                    max_duration
                );
            }
        }
    }

    /// Get performance statistics for an operation
    pub fn get_stats(&self, operation: &str) -> Option<OperationStats> {
        let counts = self.operation_counts.lock().unwrap();
        let durations = self.operation_durations.lock().unwrap();

        if let (Some(&count), Some(duration_list)) =
            (counts.get(operation), durations.get(operation))
        {
            if !duration_list.is_empty() {
                let total_duration: Duration = duration_list.iter().sum();
                let avg_duration = total_duration / duration_list.len() as u32;
                let min_duration = *duration_list.iter().min().unwrap();
                let max_duration = *duration_list.iter().max().unwrap();

                return Some(OperationStats {
                    operation_name: operation.to_string(),
                    total_count: count,
                    successful_count: duration_list.len() as u64,
                    average_duration: avg_duration,
                    min_duration,
                    max_duration,
                });
            }
        }

        None
    }
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

/// Performance statistics for an operation
#[derive(Debug, Clone)]
pub struct OperationStats {
    pub operation_name: String,
    pub total_count: u64,
    pub successful_count: u64,
    pub average_duration: Duration,
    pub min_duration: Duration,
    pub max_duration: Duration,
}

impl OperationStats {
    /// Get success rate as a percentage
    pub fn success_rate(&self) -> f64 {
        if self.total_count == 0 {
            0.0
        } else {
            (self.successful_count as f64 / self.total_count as f64) * 100.0
        }
    }
}

/// Global performance monitor instance
static PERFORMANCE_MONITOR: std::sync::OnceLock<PerformanceMonitor> = std::sync::OnceLock::new();

/// Get the global performance monitor
pub fn performance_monitor() -> &'static PerformanceMonitor {
    PERFORMANCE_MONITOR.get_or_init(PerformanceMonitor::new)
}

/// Structured logging for client events
pub struct EventLogger;

impl EventLogger {
    /// Log client lifecycle events
    pub fn lifecycle_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::info!("Client lifecycle: {} - {}", event, details),
            None => tracing::info!("Client lifecycle: {}", event),
        }
    }

    /// Log connection events
    pub fn connection_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::info!("Connection: {} - {}", event, details),
            None => tracing::info!("Connection: {}", event),
        }
    }

    /// Log authentication events
    pub fn auth_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::info!("Authentication: {} - {}", event, details),
            None => tracing::info!("Authentication: {}", event),
        }
    }

    /// Log message events
    pub fn message_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::debug!("Message: {} - {}", event, details),
            None => tracing::debug!("Message: {}", event),
        }
    }

    /// Log session events
    pub fn session_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::debug!("Session: {} - {}", event, details),
            None => tracing::debug!("Session: {}", event),
        }
    }

    /// Log error recovery events
    pub fn recovery_event(event: &str, details: Option<&str>) {
        match details {
            Some(details) => tracing::warn!("Recovery: {} - {}", event, details),
            None => tracing::warn!("Recovery: {}", event),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_operation_logger() {
        let logger = OperationLogger::start("test_operation");
        assert_eq!(logger.operation_name(), "test_operation");
        assert!(logger.elapsed() < Duration::from_millis(100));

        // Test success logging
        logger.success();
    }

    #[test]
    fn test_performance_monitor() {
        let monitor = PerformanceMonitor::new();

        // Record some operations
        monitor.record_operation("test_op", Duration::from_millis(100), true);
        monitor.record_operation("test_op", Duration::from_millis(200), true);
        monitor.record_operation("test_op", Duration::from_millis(150), false);

        // Get stats
        let stats = monitor.get_stats("test_op");
        assert!(stats.is_some());

        if let Some(stats) = stats {
            assert_eq!(stats.operation_name, "test_op");
            assert_eq!(stats.total_count, 3);
            assert_eq!(stats.successful_count, 2);
            assert!(stats.success_rate() > 60.0);
            assert!(stats.average_duration >= Duration::from_millis(100));
        }
    }

    #[test]
    fn test_operation_stats() {
        let stats = OperationStats {
            operation_name: "test".to_string(),
            total_count: 10,
            successful_count: 8,
            average_duration: Duration::from_millis(100),
            min_duration: Duration::from_millis(50),
            max_duration: Duration::from_millis(200),
        };

        assert_eq!(stats.success_rate(), 80.0);
    }
}
