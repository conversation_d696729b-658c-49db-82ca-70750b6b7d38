# WhatsApp Connection Library (Rust)

A Rust library for connecting to WhatsApp Web using WebSocket connections, implementing the WhatsApp Web protocol with proper authentication, encryption, and message handling.

## Features

- **WebSocket Connection Management**: Establish and maintain connections to WhatsApp servers
- **QR Code Authentication**: Generate QR codes for device pairing
- **End-to-End Encryption**: Full encryption support using Noise Protocol and Signal Protocol
- **Message Handling**: Send and receive text messages with proper Protocol Buffer serialization
- **Session Management**: Persistent session handling with automatic reconnection
- **Event-Driven Architecture**: Handle various WhatsApp events (messages, connection status, etc.)

## Dependencies

This project uses the following key dependencies:

### Core Runtime
- **tokio** (1.0+) - Async runtime with full features
- **tokio-tungstenite** (0.21) - WebSocket client implementation

### Protocol & Serialization
- **prost** (0.12) - Protocol Buffer implementation for Rust
- **serde** (1.0) - Serialization framework with derive macros

### Cryptography & Security
- **snow** (0.9) - Noise Protocol implementation
- **aes-gcm** (0.10) - AES-GCM encryption
- **sha2** (0.10) - SHA-2 hash functions
- **rand** (0.8) - Random number generation

### Utilities
- **qrcode** (0.14) - QR code generation for authentication
- **thiserror** (1.0) - Error handling macros
- **base64** (0.21) - Base64 encoding/decoding
- **url** (2.4) - URL parsing and manipulation

### Build Dependencies
- **prost-build** (0.12) - Protocol Buffer compilation at build time

## Project Structure

```
src/
├── lib.rs              # Library entry point and public API
├── main.rs             # Example application
├── error.rs            # Error types and handling
├── wa.rs               # WhatsApp-specific utilities
├── client/             # Client implementation
├── auth/               # Authentication and pairing logic
├── protocol/           # Protocol handling and encryption
└── types/              # Core data structures

proto/                  # Protocol Buffer definitions
build.rs               # Build script for protobuf compilation
```

## Current Status

This project is currently under development. The following components have been implemented:

- ✅ Project structure and dependencies
- ✅ Core error handling and types
- ✅ WebSocket connection management
- ✅ Session management with persistence
- ✅ QR code generation and authentication flow
- 🚧 Protocol Buffer message handling (in progress)
- 🚧 Encryption implementation (in progress)
- 🚧 Main WhatsApp client interface (planned)

## Getting Started

### Prerequisites

- Rust 1.70+ (uses 2024 edition)
- Protocol Buffer compiler (for development)

### Building

```bash
# Clone the repository
git clone <repository-url>
cd whatsmeow-rs-kiro

# Build the project
cargo build

# Run tests
cargo test

# Run the example
cargo run
```

### Basic Usage

```rust
use whatsmeow_rs_kiro::{WhatsAppClient, Message};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create a new WhatsApp client
    let mut client = WhatsAppClient::new().await?;
    
    // Connect and authenticate
    client.connect().await?;
    client.login().await?;
    
    // Send a message
    let message = Message::Text("Hello, WhatsApp!".to_string());
    client.send_message("<EMAIL>", message).await?;
    
    // Handle incoming events
    while let Some(event) = client.next_event().await {
        match event {
            Event::MessageReceived(msg) => {
                println!("Received: {:?}", msg);
            }
            Event::ConnectionStatusChanged(status) => {
                println!("Connection status: {:?}", status);
            }
            _ => {}
        }
    }
    
    Ok(())
}
```

## Architecture

### Connection Management
The library maintains a persistent WebSocket connection to WhatsApp servers with automatic reconnection handling and exponential backoff for reliability.

### Authentication Flow
1. **QR Code Generation**: Create a unique QR code with reference ID and public key
2. **Display QR Code**: Show the QR code in terminal for scanning
3. **Mobile App Scanning**: Wait for WhatsApp mobile app to scan the code (2-minute timeout)
4. **Pairing Process**: Complete the device pairing handshake
5. **Session Establishment**: Create and save encrypted session for future use

The QR code scanning process has a 2-minute timeout, providing sufficient time for users to open WhatsApp on their mobile device and scan the code.

#### QR Code Authentication Example
```rust
use whatsmeow_rs_kiro::auth::QRGenerator;

// Generate and display QR code
let qr_generator = QRGenerator::new();
qr_generator.display_qr()?;

// The QR code contains: ref_id,public_key_base64,1
let qr_data = qr_generator.generate_qr_data();
println!("QR Data: {}", qr_data);
```

### Message Encryption
All messages are encrypted using the Signal Protocol with proper key exchange and forward secrecy. The library handles:
- Noise Protocol for initial handshake
- Signal Protocol for message encryption
- Key rotation and management
- Group message encryption

### Error Handling
Comprehensive error handling with specific error types for different failure scenarios:
- Connection errors
- Authentication failures
- Protocol errors
- Encryption issues
- Serialization problems

## Development

### Running Tests

```bash
# Run all tests
cargo test

# Run specific test module
cargo test client::tests

# Run with output
cargo test -- --nocapture
```

### Protocol Buffer Compilation

Protocol Buffer files are automatically compiled during the build process using the `build.rs` script. The generated Rust code is included in the build output.

## Contributing

This project is currently in active development. Please check the implementation plan in `.kiro/specs/whatsapp-connection-rust/tasks.md` for current progress and planned features.

## License

[Add your license information here]

## Disclaimer

This library is for educational and research purposes. Make sure to comply with WhatsApp's Terms of Service when using this library.