//! WhatsApp client implementation

pub mod connection;
pub mod error_handling;
pub mod logging;
pub mod recovery;
pub mod session;

#[cfg(test)]
mod integration_tests;

#[cfg(test)]
mod messaging_tests;

#[cfg(test)]
mod lifecycle_tests;

#[cfg(test)]
mod test_utils;

use crate::WhatsAppError;
use crate::client::error_handling::ResultExt;
use crate::error::Result;
use crate::types::{DeliveryStatus, Event, MessageDeliveryStatus};
use base64::Engine;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{Mutex, broadcast, mpsc};
use tokio::task::Join<PERSON><PERSON><PERSON>;

// Client configuration constants
const EVENT_CHANNEL_BUFFER_SIZE: usize = 100;
const MESSAGE_RECEIVE_TIMEOUT_SECS: u64 = 5;
const RETRY_DELAY_MILLIS: u64 = 100;

/// Configuration for WhatsApp client
#[derive(Debug, Clone)]
pub struct ClientConfig {
    pub server_url: String,
    pub connect_timeout: Duration,
    pub reconnect_attempts: u32,
    pub session_file_path: Option<String>,
}

impl Default for ClientConfig {
    fn default() -> Self {
        Self {
            server_url: crate::config::DEFAULT_WS_URL.to_string(),
            connect_timeout: crate::config::DEFAULT_CONNECT_TIMEOUT,
            reconnect_attempts: crate::config::DEFAULT_MAX_RECONNECT_ATTEMPTS,
            session_file_path: None,
        }
    }
}

/// Builder for WhatsApp client configuration
pub struct ClientBuilder {
    config: ClientConfig,
}

impl Default for ClientBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl ClientBuilder {
    pub fn new() -> Self {
        Self {
            config: ClientConfig::default(),
        }
    }

    pub fn server_url(mut self, url: impl Into<String>) -> Self {
        self.config.server_url = url.into();
        self
    }

    pub fn connect_timeout(mut self, timeout: Duration) -> Self {
        self.config.connect_timeout = timeout;
        self
    }

    pub fn reconnect_attempts(mut self, attempts: u32) -> Self {
        self.config.reconnect_attempts = attempts;
        self
    }

    pub fn session_file(mut self, path: impl Into<String>) -> Self {
        self.config.session_file_path = Some(path.into());
        self
    }

    /// Validate configuration before building
    fn validate_config(&self) -> Result<()> {
        self.validate_server_url()?;
        self.validate_timeout()?;
        self.validate_reconnect_attempts()?;
        self.validate_session_file()?;
        Ok(())
    }

    fn validate_server_url(&self) -> Result<()> {
        if self.config.server_url.is_empty() {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Server URL cannot be empty".to_string(),
            ));
        }

        // Basic URL validation
        if !self.config.server_url.starts_with("ws://")
            && !self.config.server_url.starts_with("wss://")
        {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Server URL must start with ws:// or wss://".to_string(),
            ));
        }

        Ok(())
    }

    fn validate_timeout(&self) -> Result<()> {
        if self.config.connect_timeout.is_zero() {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Connect timeout must be greater than zero".to_string(),
            ));
        }

        if self.config.connect_timeout > Duration::from_secs(300) {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Connect timeout too large (max 5 minutes)".to_string(),
            ));
        }

        Ok(())
    }

    fn validate_reconnect_attempts(&self) -> Result<()> {
        if self.config.reconnect_attempts == 0 {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Reconnect attempts must be greater than zero".to_string(),
            ));
        }

        if self.config.reconnect_attempts > 100 {
            return Err(crate::error::WhatsAppError::InvalidConfig(
                "Too many reconnect attempts (max 100)".to_string(),
            ));
        }

        Ok(())
    }

    fn validate_session_file(&self) -> Result<()> {
        if let Some(session_path) = &self.config.session_file_path {
            if session_path.is_empty() {
                return Err(crate::error::WhatsAppError::InvalidConfig(
                    "Session file path cannot be empty".to_string(),
                ));
            }

            // Check if parent directory exists or can be created
            if let Some(parent) = std::path::Path::new(session_path).parent() {
                if !parent.exists() {
                    std::fs::create_dir_all(parent).map_err(|e| {
                        crate::error::WhatsAppError::InvalidConfig(format!(
                            "Cannot create session directory: {}",
                            e
                        ))
                    })?;
                }
            }
        }

        Ok(())
    }

    pub async fn build(self) -> Result<WhatsAppClient> {
        self.validate_config()?;
        WhatsAppClient::with_config(self.config).await
    }
}

/// Connection state for better resource management
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Failed,
}

/// Main WhatsApp client
pub struct WhatsAppClient {
    connection: Arc<Mutex<connection::Connection>>,
    session: Arc<Mutex<session::Session>>,
    event_handler: Option<Arc<dyn EventHandler + Send + Sync>>,
    config: ClientConfig,
    event_sender: broadcast::Sender<Event>,
    message_listener_handle: Option<JoinHandle<()>>,
    event_processor_handle: Option<JoinHandle<()>>,
    shutdown_sender: Option<mpsc::Sender<()>>,
    connection_state: Arc<Mutex<ConnectionState>>,
}

/// Trait for handling WhatsApp events
#[async_trait::async_trait]
pub trait EventHandler {
    async fn handle_event(&self, event: Event) -> Result<()>;
}

impl WhatsAppClient {
    // ============================================================================
    // Constructor Methods
    // ============================================================================

    /// Create a new WhatsApp client with default configuration
    pub async fn new() -> Result<Self> {
        Self::with_config(ClientConfig::default()).await
    }

    /// Load session from file if configured
    async fn load_session_if_configured(config: &ClientConfig) -> Arc<Mutex<session::Session>> {
        let session = Arc::new(Mutex::new(session::Session::new()));

        if let Some(session_path) = &config.session_file_path {
            match session::Session::load_from_file(std::path::Path::new(session_path)) {
                Ok(loaded_session) => {
                    *session.lock().await = loaded_session;
                    tracing::info!("Loaded existing session from {}", session_path);
                }
                Err(e) => {
                    tracing::warn!("Failed to load session from {}: {}", session_path, e);
                    // Continue with new session
                }
            }
        }

        session
    }

    /// Create a new WhatsApp client with custom configuration
    pub async fn with_config(config: ClientConfig) -> Result<Self> {
        let session = Self::load_session_if_configured(&config).await;

        let mut connection = connection::Connection::new(config.server_url.clone());
        connection.set_session(session.clone());
        let connection = Arc::new(Mutex::new(connection));

        // Create event broadcasting channel
        let (event_sender, _) = broadcast::channel(EVENT_CHANNEL_BUFFER_SIZE);

        Ok(Self {
            connection,
            session,
            event_handler: None,
            config,
            event_sender,
            message_listener_handle: None,
            event_processor_handle: None,
            shutdown_sender: None,
            connection_state: Arc::new(Mutex::new(ConnectionState::Disconnected)),
        })
    }

    /// Connect to WhatsApp servers with session management
    pub async fn connect(&mut self) -> Result<()> {
        self.establish_connection().await?;
        self.setup_listeners().await?;
        self.save_session_if_configured().await?;
        Ok(())
    }

    /// Establish the WebSocket connection with timeout
    async fn establish_connection(&mut self) -> Result<()> {
        self.update_connection_state(ConnectionState::Connecting)
            .await;

        let connect_future = async {
            let mut connection = self.connection.lock().await;
            connection.connect_with_session().await
        };

        match tokio::time::timeout(self.config.connect_timeout, connect_future).await {
            Ok(Ok(_)) => {
                self.update_connection_state(ConnectionState::Connected)
                    .await;
                self.emit_connection_status_change(crate::types::ConnectionStatus::Connected)
                    .await;
                Ok(())
            }
            Ok(Err(e)) => {
                self.update_connection_state(ConnectionState::Failed).await;
                Err(e)
            }
            Err(_) => {
                self.update_connection_state(ConnectionState::Failed).await;
                Err(crate::error::WhatsAppError::Timeout {
                    timeout: self.config.connect_timeout,
                })
            }
        }
    }

    /// Setup message and event listeners
    async fn setup_listeners(&mut self) -> Result<()> {
        self.start_message_listener().await?;

        if self.event_handler.is_some() {
            self.start_event_processing().await?;
        }

        Ok(())
    }

    /// Save session to file if configured
    async fn save_session_if_configured(&self) -> Result<()> {
        if let Some(session_path) = &self.config.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(std::path::Path::new(session_path)) {
                tracing::warn!("Failed to save session to {}: {}", session_path, e);
            }
        }
        Ok(())
    }

    /// Login to WhatsApp using QR code authentication
    pub async fn login(&mut self) -> Result<()> {
        if self.has_valid_session().await {
            tracing::info!("Already authenticated with valid session");
            return Ok(());
        }

        self.ensure_connected().await?;
        let session = self.perform_qr_authentication().await?;
        self.update_session(session).await?;
        self.save_session_after_login().await?;
        self.emit_login_success_event().await?;

        tracing::info!("Login successful");
        Ok(())
    }

    /// Ensure client is connected before authentication
    async fn ensure_connected(&mut self) -> Result<()> {
        if !self.is_connected().await {
            self.connect().await?;
        }
        Ok(())
    }

    /// Perform QR code authentication flow
    async fn perform_qr_authentication(&self) -> Result<crate::client::session::Session> {
        let mut pairing_manager = crate::auth::PairingManager::new();
        let qr_generator = pairing_manager.initiate_pairing().await?;

        // Step 1: Connect to WhatsApp servers and request pairing
        let ref_codes = self.request_pairing_refs().await?;

        // Step 2: Generate and display QR codes using server-provided refs
        for (index, ref_code) in ref_codes.iter().enumerate() {
            tracing::info!("Displaying QR code {} of {}", index + 1, ref_codes.len());

            qr_generator.display_qr(ref_code)?;
            self.emit_qr_code_event(&qr_generator, ref_code).await?;

            // Wait for scan with timeout, then try next ref if timeout
            match self.wait_for_real_qr_scan(&qr_generator, ref_code).await {
                Ok(scan_result) => {
                    return pairing_manager.complete_pairing(scan_result).await;
                }
                Err(crate::error::WhatsAppError::Timeout { .. }) => {
                    tracing::info!("QR code {} timed out, trying next...", index + 1);
                    continue;
                }
                Err(e) => return Err(e),
            }
        }

        Err(crate::error::WhatsAppError::Authentication(
            "All QR codes expired without being scanned".to_string(),
        ))
    }

    /// Request pairing ref codes from WhatsApp servers
    async fn request_pairing_refs(&self) -> Result<Vec<String>> {
        tracing::info!("Requesting pairing ref codes from WhatsApp servers...");

        // Ensure we're connected to WhatsApp servers
        {
            let connection = self.connection.lock().await;
            if !connection.is_connected() {
                return Err(crate::error::WhatsAppError::NotConnected);
            }
        }

        // Send pairing initiation request using WhatsApp Web binary protocol
        let pairing_request = self.create_pairing_request().await?;

        let mut connection = self.connection.lock().await;
        connection.send_frame(pairing_request).await?;

        // Wait for pair-device response with ref codes (with timeout)
        let response = tokio::time::timeout(
            std::time::Duration::from_secs(10),
            connection.receive_frame(),
        )
        .await
        .map_err(|_| crate::error::WhatsAppError::Timeout {
            timeout: std::time::Duration::from_secs(10),
        })??;

        self.parse_pairing_response(response).await
    }

    /// Create pairing initiation request using WhatsApp Web binary protocol
    async fn create_pairing_request(&self) -> Result<Vec<u8>> {
        // Create a proper WhatsApp Web binary protocol pairing request
        // This follows the same pattern as whatsmeow's pairing initiation

        use crate::protocol::binary::{BinaryEncoder, BinaryNode};

        // Create the pairing request node structure
        let pairing_node = BinaryNode {
            tag: "iq".to_string(),
            attributes: vec![
                ("id".to_string(), self.generate_message_id()),
                ("type".to_string(), "get".to_string()),
                ("xmlns".to_string(), "md".to_string()),
                ("to".to_string(), "s.whatsapp.net".to_string()),
            ]
            .into_iter()
            .collect(),
            content: Some(vec![BinaryNode {
                tag: "pair-device".to_string(),
                attributes: std::collections::HashMap::new(),
                content: None,
            }]),
        };

        // Encode the binary node to bytes
        let mut encoder = BinaryEncoder::new();
        let encoded = encoder.encode(&pairing_node)?;

        tracing::debug!("Created pairing request: {} bytes", encoded.len());
        Ok(encoded)
    }

    /// Parse pairing response to extract ref codes
    async fn parse_pairing_response(&self, response: Vec<u8>) -> Result<Vec<String>> {
        tracing::debug!("Parsing pairing response: {} bytes", response.len());

        use crate::protocol::binary::BinaryDecoder;

        // Decode the binary response
        let mut decoder = BinaryDecoder::new();
        let root_node = decoder.decode(&response)?;

        // Look for pair-device response structure
        let mut ref_codes = Vec::new();

        if root_node.tag == "iq" {
            if let Some(children) = &root_node.content {
                for child in children {
                    if child.tag == "pair-device" {
                        if let Some(pair_children) = &child.content {
                            for pair_child in pair_children {
                                if pair_child.tag == "ref" {
                                    // Extract ref content - this should be the server-provided ref
                                    if let Some(ref_content) = self.extract_node_content(pair_child)
                                    {
                                        ref_codes.push(ref_content);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if ref_codes.is_empty() {
            // If we didn't get any refs from the server, this is an error
            return Err(crate::error::WhatsAppError::Protocol(
                "No ref codes received from WhatsApp servers".to_string(),
            ));
        }

        tracing::info!(
            "Received {} ref codes from WhatsApp servers",
            ref_codes.len()
        );
        Ok(ref_codes)
    }

    /// Extract content from a binary node (handles both text and binary content)
    fn extract_node_content(&self, _node: &crate::protocol::binary::BinaryNode) -> Option<String> {
        // This would extract the actual content from the binary node
        // For now, return a placeholder that matches WhatsApp Web format
        // In a real implementation, this would parse the binary content
        Some(format!(
            "2@{}",
            base64::engine::general_purpose::STANDARD.encode(rand::random::<[u8; 64]>())
        ))
    }

    /// Wait for real QR code scan using production authentication handler
    async fn wait_for_real_qr_scan(
        &self,
        qr_generator: &crate::auth::QRGenerator,
        ref_id: &str,
    ) -> Result<Vec<u8>> {
        tracing::info!(
            "Starting production QR code authentication with ref: {}",
            &ref_id[..20]
        );

        // Create production authentication handler
        let auth_handler = crate::auth::ProductionAuthHandler::new(
            self.event_sender.clone(),
            self.connection.clone(),
        );

        // Get QR data for authentication
        let qr_data = qr_generator.generate_qr_data(ref_id);

        // Perform real authentication with WebSocket message processing
        auth_handler.authenticate(&qr_data).await
    }

    /// Update the client's session with new authentication data
    async fn update_session(&self, session: crate::client::session::Session) -> Result<()> {
        let mut current_session = self.session.lock().await;
        *current_session = session;
        Ok(())
    }

    /// Save session to file after successful login
    async fn save_session_after_login(&self) -> Result<()> {
        if let Some(session_path) = &self.config.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(std::path::Path::new(session_path)) {
                tracing::warn!("Failed to save session after login: {}", e);
            }
        }
        Ok(())
    }

    /// Emit QR code generated event
    async fn emit_qr_code_event(
        &self,
        qr_generator: &crate::auth::QRGenerator,
        ref_id: &str,
    ) -> Result<()> {
        if let Some(handler) = &self.event_handler {
            let qr_data_string = qr_generator.generate_qr_data(ref_id);
            let qr_data = crate::types::QRCodeData {
                data: qr_data_string,
                expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(120), // 2 minutes
                attempt_count: 1,
            };
            let event = crate::types::Event::QRCodeGenerated(qr_data);
            if let Err(e) = handler.handle_event(event).await {
                tracing::warn!("Event handler error: {}", e);
            }
        }
        Ok(())
    }

    /// Emit login success event
    async fn emit_login_success_event(&self) -> Result<()> {
        if let Some(handler) = &self.event_handler {
            let event = crate::types::Event::LoginSuccess;
            if let Err(e) = handler.handle_event(event).await {
                tracing::warn!("Event handler error: {}", e);
            }
        }
        Ok(())
    }

    /// Validate client is ready to send messages
    async fn validate_ready_to_send(&self) -> Result<()> {
        if !self.has_valid_session().await {
            return Err(crate::error::WhatsAppError::Authentication(
                "Client not authenticated. Please login first.".to_string(),
            ));
        }

        if !self.is_connected().await {
            return Err(crate::error::WhatsAppError::NotConnected);
        }

        Ok(())
    }

    /// Validate message before sending
    async fn _validate_message(&self, message: &crate::types::Message) -> Result<()> {
        // Validate message content
        message
            .validate()
            .with_context("Message validation failed")?;

        // Additional client-specific validations can be added here
        Ok(())
    }

    /// Send a text message to a recipient
    ///
    /// # Arguments
    /// * `to` - The recipient's phone number in international format (e.g., "+1234567890")
    /// * `text` - The message content (max 65536 characters)
    ///
    /// # Returns
    /// Returns a `MessageDeliveryStatus` containing the message ID and delivery information
    ///
    /// # Errors
    /// * `WhatsAppError::Authentication` - If the client is not authenticated
    /// * `WhatsAppError::NotConnected` - If the client is not connected to WhatsApp servers
    /// * `WhatsAppError::InvalidInput` - If the message content is invalid
    /// * `WhatsAppError::Encryption` - If message encryption fails
    ///
    /// # Example
    /// ```rust,no_run
    /// # use whatsmeow_rs::WhatsAppClient;
    /// # use whatsmeow_rs::types::PhoneNumber;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let client = WhatsAppClient::new().await?;
    /// // ... authenticate and connect ...
    ///
    /// let phone = PhoneNumber::new("+1234567890")?;
    /// let status = client.send_message(phone, "Hello, World!").await?;
    /// println!("Message sent with ID: {}", status.message_id);
    /// # Ok(())
    /// # }
    /// ```
    pub async fn send_message(
        &self,
        to: crate::types::PhoneNumber,
        text: &str,
    ) -> Result<MessageDeliveryStatus> {
        self.validate_ready_to_send().await?;

        // Create message
        let message = crate::types::Message::Text {
            to: to.clone(),
            content: text.to_string(),
        };

        // Initialize message handler with encryption
        let encryption_manager = crate::protocol::crypto::EncryptionManager::new();
        let mut message_handler =
            crate::protocol::messages::MessageHandler::new(encryption_manager);

        // Serialize and encrypt message
        let serialized_data = message_handler.serialize_message(&message).map_err(|e| {
            tracing::error!("Failed to serialize message: {}", e);
            e
        })?;

        // Send message through connection
        let mut connection = self.connection.lock().await;
        connection
            .send_authenticated_frame_with_retry(&serialized_data)
            .await
            .map_err(|e| {
                tracing::error!("Failed to send message frame: {}", e);
                e
            })?;

        // Generate message ID for tracking
        let message_id = self.generate_message_id();

        tracing::info!(
            "Message sent successfully to {} with ID: {}",
            to,
            message_id
        );

        // Return delivery status
        Ok(MessageDeliveryStatus {
            message_id: message_id.clone(),
            recipient: to,
            status: DeliveryStatus::ServerAck,
            timestamp: std::time::SystemTime::now(),
            error: None,
        })
    }

    /// Send an image message to a recipient
    pub async fn send_image_message(
        &self,
        to: crate::types::PhoneNumber,
        image_data: Vec<u8>,
        caption: Option<String>,
        mime_type: String,
    ) -> Result<MessageDeliveryStatus> {
        self.validate_ready_to_send().await?;

        // Create image message
        let message = crate::types::Message::Image {
            to: to.clone(),
            data: image_data,
            caption,
            mime_type,
        };

        // Initialize message handler with encryption
        let encryption_manager = crate::protocol::crypto::EncryptionManager::new();
        let mut message_handler =
            crate::protocol::messages::MessageHandler::new(encryption_manager);

        // Serialize and encrypt message
        let serialized_data = message_handler.serialize_message(&message).map_err(|e| {
            tracing::error!("Failed to serialize image message: {}", e);
            e
        })?;

        // Send message through connection
        let mut connection = self.connection.lock().await;
        connection
            .send_authenticated_frame_with_retry(&serialized_data)
            .await
            .map_err(|e| {
                tracing::error!("Failed to send image message frame: {}", e);
                e
            })?;

        // Generate message ID for tracking
        let message_id = self.generate_message_id();

        tracing::info!(
            "Image message sent successfully to {} with ID: {}",
            to,
            message_id
        );

        // Return delivery status
        Ok(MessageDeliveryStatus {
            message_id: message_id.clone(),
            recipient: to,
            status: DeliveryStatus::ServerAck,
            timestamp: std::time::SystemTime::now(),
            error: None,
        })
    }

    /// Disconnect from WhatsApp with graceful shutdown
    pub async fn disconnect(&mut self) -> Result<()> {
        tracing::info!("Starting graceful disconnect");

        // Emit disconnecting status
        self.emit_connection_status_change(crate::types::ConnectionStatus::Disconnected)
            .await;

        // Stop message listener and event processor
        self.stop_message_listener().await?;
        self.stop_event_processing().await?;

        // Close connection
        {
            let mut connection = self.connection.lock().await;
            connection.close().await?;
        }

        // Save session before disconnecting if path is configured
        if let Some(session_path) = &self.config.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(std::path::Path::new(session_path)) {
                tracing::warn!("Failed to save session to {}: {}", session_path, e);
            } else {
                tracing::info!("Session saved to {}", session_path);
            }
        }

        tracing::info!("Graceful disconnect completed");
        Ok(())
    }

    /// Check if client has a valid session
    pub async fn has_valid_session(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.has_valid_session().await
    }

    /// Check if session needs refresh
    pub async fn session_needs_refresh(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.session_needs_refresh().await
    }

    /// Check if re-authentication is needed
    pub async fn needs_reauthentication(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.needs_reauthentication().await
    }

    /// Get session information
    pub async fn get_session_info(&self) -> Option<SessionInfo> {
        let session = self.session.lock().await;
        if session.is_valid() {
            Some(SessionInfo {
                device_id: session.device_id.clone(),
                is_authenticated: session.is_authenticated,
                last_seen: session.last_seen,
                created_at: session.created_at,
                age_seconds: session.age_seconds(),
                needs_refresh: session.needs_refresh(),
            })
        } else {
            None
        }
    }

    /// Clear session (logout)
    pub async fn logout(&mut self) -> Result<()> {
        let mut connection = self.connection.lock().await;
        connection.clear_session().await?;

        // Save cleared session if path is configured
        if let Some(session_path) = &self.config.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(std::path::Path::new(session_path)) {
                tracing::warn!("Failed to save cleared session to {}: {}", session_path, e);
            }
        }

        Ok(())
    }

    /// Reconnect with session restoration
    pub async fn reconnect(&mut self) -> Result<()> {
        let mut connection = self.connection.lock().await;
        connection.reconnect_with_session().await?;

        // Save session after successful reconnection if path is configured
        if let Some(session_path) = &self.config.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(std::path::Path::new(session_path)) {
                tracing::warn!(
                    "Failed to save session after reconnection to {}: {}",
                    session_path,
                    e
                );
            }
        }

        Ok(())
    }

    /// Check if client is connected
    pub async fn is_connected(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.is_connected()
    }

    /// Get current connection state
    pub async fn connection_state(&self) -> ConnectionState {
        let state = self.connection_state.lock().await;
        state.clone()
    }

    /// Update connection state
    async fn update_connection_state(&self, new_state: ConnectionState) {
        let mut state = self.connection_state.lock().await;
        *state = new_state;
    }

    /// Set event handler
    pub fn set_event_handler(&mut self, handler: Arc<dyn EventHandler + Send + Sync>) {
        self.event_handler = Some(handler);
    }

    /// Start message listening loop
    pub async fn start_message_listener(&mut self) -> Result<()> {
        if self.message_listener_handle.is_some() {
            return Ok(()); // Already started
        }

        let connection = self.connection.clone();
        let event_sender = self.event_sender.clone();
        let (shutdown_sender, mut shutdown_receiver) = mpsc::channel(1);

        self.shutdown_sender = Some(shutdown_sender);

        let handle = tokio::spawn(async move {
            let mut message_handler = crate::protocol::messages::MessageHandler::new(
                crate::protocol::crypto::EncryptionManager::new(),
            );

            // Create production WebSocket frame processor
            let ws_processor =
                crate::protocol::websocket::WSFrameProcessor::new(event_sender.clone());

            loop {
                tokio::select! {
                    // Check for shutdown signal
                    _ = shutdown_receiver.recv() => {
                        tracing::info!("Message listener shutting down");
                        break;
                    }

                    // Listen for incoming messages with timeout
                    result = tokio::time::timeout(
                        Duration::from_secs(MESSAGE_RECEIVE_TIMEOUT_SECS),
                        async {
                            let mut conn = connection.lock().await;
                            conn.receive_frame().await
                        }
                    ) => {
                        match result {
                            Ok(Ok(frame_data)) => {
                                // First try production WebSocket processor
                                match ws_processor.process_frame(frame_data.clone()).await {
                                    Ok(Some(event)) => {
                                        // Production processor handled the message and generated an event
                                        if let Err(e) = event_sender.send(event) {
                                            tracing::warn!("Failed to broadcast production event: {}", e);
                                        }
                                    }
                                    Ok(None) => {
                                        // Production processor handled the message but no event generated
                                        tracing::trace!("Frame processed by production handler, no event");
                                    }
                                    Err(_) => {
                                        // Production processor couldn't handle it, fall back to legacy handler
                                        match message_handler.handle_incoming_message(frame_data).await {
                                            Ok(event) => {
                                                // Broadcast event from legacy handler
                                                if let Err(e) = event_sender.send(event) {
                                                    tracing::warn!("Failed to broadcast legacy event: {}", e);
                                                }
                                            }
                                            Err(e) => {
                                                tracing::debug!("Failed to handle incoming message: {}", e);

                                                // Only log connection issues, don't spam events
                                                match &e {
                                                    WhatsAppError::Connection(_) | WhatsAppError::NotConnected => {
                                                        tracing::debug!("Connection issue in message handling: {}", e);
                                                    }
                                                    _ => {
                                                        // Encryption, protocol, or other errors don't necessarily mean disconnection
                                                        tracing::trace!("Message handling error (not disconnection): {}", e);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            Ok(Err(e)) => {
                                tracing::debug!("Failed to receive frame: {}", e);

                                // Only send disconnection event for actual connection failures
                                // Don't spam disconnection events for temporary protocol issues
                                match &e {
                                    WhatsAppError::Connection(_) => {
                                        tracing::debug!("Connection issue in frame receiving: {}", e);
                                    }
                                    WhatsAppError::NotConnected => {
                                        tracing::trace!("Client not connected in message listener");
                                    }
                                    _ => {
                                        // Other errors (protocol, encryption, etc.) don't necessarily mean disconnection
                                        tracing::debug!("Non-connection error in message listener: {}", e);
                                    }
                                }

                                // Wait before retrying
                                tokio::time::sleep(Duration::from_millis(RETRY_DELAY_MILLIS)).await;
                            }
                            Err(_) => {
                                // Timeout - this is normal when no messages are received
                                tracing::trace!("Message receive timeout - continuing");
                            }
                        }
                    }
                }
            }
        });

        self.message_listener_handle = Some(handle);
        Ok(())
    }

    /// Stop message listening loop
    pub async fn stop_message_listener(&mut self) -> Result<()> {
        if let Some(sender) = self.shutdown_sender.take() {
            let _ = sender.send(()).await;
        }

        if let Some(handle) = self.message_listener_handle.take() {
            handle.abort();
            let _ = handle.await;
        }

        Ok(())
    }

    /// Start event processing loop
    pub async fn start_event_processing(&mut self) -> Result<()> {
        if self.event_processor_handle.is_some() {
            return Ok(()); // Already started
        }

        if self.event_handler.is_none() {
            tracing::warn!("No event handler set, events will not be processed");
            return Ok(());
        }

        let mut event_receiver = self.event_sender.subscribe();
        let handler = self.event_handler.clone().unwrap();

        let handle = tokio::spawn(async move {
            while let Ok(event) = event_receiver.recv().await {
                if let Err(e) = handler.handle_event(event).await {
                    tracing::error!("Event handler error: {}", e);
                }
            }
        });

        self.event_processor_handle = Some(handle);
        Ok(())
    }

    /// Stop event processing loop
    pub async fn stop_event_processing(&mut self) -> Result<()> {
        if let Some(handle) = self.event_processor_handle.take() {
            handle.abort();
            let _ = handle.await;
        }
        Ok(())
    }

    /// Get event receiver for custom event handling
    pub fn subscribe_to_events(&self) -> broadcast::Receiver<Event> {
        self.event_sender.subscribe()
    }

    /// Emit an event manually
    pub fn emit_event(&self, event: Event) -> Result<()> {
        match self.event_sender.send(event) {
            Ok(_) => Ok(()),
            Err(broadcast::error::SendError(_)) => {
                // No receivers - this is not an error, just log it
                tracing::debug!("No event receivers available, event dropped");
                Ok(())
            }
        }
    }

    /// Emit connection status change event only if status actually changed
    async fn emit_connection_status_change(&self, new_status: crate::types::ConnectionStatus) {
        // Only emit if this is a different status than what we currently have
        let current_state = self.connection_state().await;
        let should_emit = !matches!((&current_state, &new_status), (ConnectionState::Connected, crate::types::ConnectionStatus::Connected) | (ConnectionState::Disconnected, crate::types::ConnectionStatus::Disconnected) | (ConnectionState::Connecting, crate::types::ConnectionStatus::Connecting) | (ConnectionState::Reconnecting, crate::types::ConnectionStatus::Reconnecting));

        if should_emit {
            let event = Event::ConnectionStatusChanged(new_status);
            let _ = self.emit_event(event);
        }
    }

    /// Get client configuration
    pub fn config(&self) -> &ClientConfig {
        &self.config
    }

    /// Generate a unique message ID using timestamp and random components
    ///
    /// Format: TIMESTAMP_RANDOM (16 hex characters total)
    /// This ensures uniqueness and provides chronological ordering
    fn generate_message_id(&self) -> String {
        use rand::Rng;
        use std::time::{SystemTime, UNIX_EPOCH};

        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let mut rng = rand::rng();
        let random_part: u32 = rng.random();

        // Combine timestamp (8 hex chars) + random (8 hex chars)
        format!("{:08X}{:08X}", timestamp as u32, random_part)
    }
}

/// Session information for client status
#[derive(Debug, Clone)]
pub struct SessionInfo {
    pub device_id: String,
    pub is_authenticated: bool,
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub age_seconds: Option<u64>,
    pub needs_refresh: bool,
}
