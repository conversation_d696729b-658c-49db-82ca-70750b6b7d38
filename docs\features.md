# Features

## Optional Features

- `qr-terminal` - Display QR codes in terminal (enabled by default)
- `image-support` - Support for sending image messages
- `session-encryption` - Encrypt stored session files

## Configuration

```toml
[dependencies]
whatsmeow-rs = { version = "0.1", features = ["image-support"] }
```

## Feature Details

### QR Terminal Display
Displays QR codes directly in the terminal for easy scanning. The QR code authentication process includes a 2-minute timeout, giving users sufficient time to open WhatsApp on their mobile device and complete the scanning process.

### Image Support
Enables sending image messages with captions.

### Session Encryption
Encrypts session files on disk for additional security.