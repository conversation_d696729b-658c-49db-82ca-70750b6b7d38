//! Comprehensive integration tests for complete WhatsApp client workflows
//!
//! This module tests end-to-end functionality including:
//! - Full authentication flow from QR generation to successful login
//! - End-to-end message sending and receiving
//! - Reconnection scenarios with simulated network failures
//! - Session persistence and restoration functionality

use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::Mutex;
use tokio::time::timeout;
use whatsmeow_rs::*;

/// Mock event handler for testing
#[derive(Debug, Default)]
struct TestEventHandler {
    events: Arc<Mutex<Vec<Event>>>,
}

impl TestEventHandler {
    fn new() -> Self {
        Self {
            events: Arc::new(Mutex::new(Vec::new())),
        }
    }

    async fn get_events(&self) -> Vec<Event> {
        self.events.lock().await.clone()
    }

    async fn _clear_events(&self) {
        self.events.lock().await.clear();
    }

    async fn _wait_for_event_type(&self, timeout_duration: Duration) -> Option<Event> {
        let start = SystemTime::now();
        while start.elapsed().unwrap_or_default() < timeout_duration {
            let events = self.events.lock().await;
            if let Some(event) = events.last() {
                return Some(event.clone());
            }
            drop(events);
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        None
    }
}

#[async_trait::async_trait]
impl EventHandler for TestEventHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        let mut events = self.events.lock().await;
        events.push(event);
        Ok(())
    }
}

/// Test full authentication flow from QR generation to successful login
#[tokio::test]
async fn test_full_authentication_flow() {
    // Create client with test configuration
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(10))
        .reconnect_attempts(3)
        .session_file("test_session_auth.json")
        .build()
        .await
        .expect("Failed to create client");

    // Set up event handler to capture authentication events
    let event_handler = Arc::new(TestEventHandler::new());
    client.set_event_handler(event_handler.clone());

    // Test connection establishment
    let connect_result = client.connect().await;
    assert!(
        connect_result.is_ok(),
        "Connection should succeed: {:?}",
        connect_result
    );

    // Verify connection state
    assert!(client.is_connected().await, "Client should be connected");

    // Test QR code generation and authentication flow
    let login_result = timeout(Duration::from_secs(30), client.login()).await;

    match login_result {
        Ok(Ok(())) => {
            // Login succeeded - verify events were generated
            let events = event_handler.get_events().await;

            // Should have QR code generation event
            let has_qr_event = events
                .iter()
                .any(|e| matches!(e, Event::QRCodeGenerated(_)));
            assert!(has_qr_event, "Should have QR code generation event");

            // Should have login success event
            let has_login_success = events.iter().any(|e| matches!(e, Event::LoginSuccess));
            assert!(has_login_success, "Should have login success event");

            // Verify session is valid
            assert!(
                client.has_valid_session().await,
                "Should have valid session after login"
            );

            // Verify session info is available
            let session_info = client.get_session_info().await;
            assert!(session_info.is_some(), "Session info should be available");

            if let Some(info) = session_info {
                assert!(info.is_authenticated, "Session should be authenticated");
                assert!(!info.device_id.is_empty(), "Device ID should not be empty");
            }
        }
        Ok(Err(e)) => {
            // Login failed - this might be expected in test environment
            println!("Login failed (expected in test): {:?}", e);

            // Still verify QR code was generated
            let events = event_handler.get_events().await;
            let has_qr_event = events
                .iter()
                .any(|e| matches!(e, Event::QRCodeGenerated(_)));
            assert!(
                has_qr_event,
                "Should have QR code generation event even if login fails"
            );
        }
        Err(_) => {
            panic!("Login operation timed out");
        }
    }

    // Clean up
    let _ = client.disconnect().await;
    let _ = std::fs::remove_file("test_session_auth.json");
}

/// Test end-to-end message sending and receiving
#[tokio::test]
async fn test_message_sending_and_receiving() {
    // Create and configure client
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(10))
        .session_file("test_session_messaging.json")
        .build()
        .await
        .expect("Failed to create client");

    let event_handler = Arc::new(TestEventHandler::new());
    client.set_event_handler(event_handler.clone());

    // Connect and attempt authentication
    let _ = client.connect().await;

    // Create test phone number
    let test_phone = PhoneNumber::new("+1234567890").expect("Failed to create test phone number");

    // Test text message sending
    let text_message_result = client
        .send_message(test_phone.clone(), "Test message from integration test")
        .await;

    match text_message_result {
        Ok(delivery_status) => {
            // Verify delivery status structure
            assert!(
                !delivery_status.message_id.is_empty(),
                "Message ID should not be empty"
            );
            assert_eq!(
                delivery_status.recipient, test_phone,
                "Recipient should match"
            );
            assert!(
                matches!(
                    delivery_status.status,
                    DeliveryStatus::ServerAck | DeliveryStatus::Sent | DeliveryStatus::Pending
                ),
                "Status should indicate message was processed"
            );

            println!("Text message sent successfully: {:?}", delivery_status);
        }
        Err(WhatsAppError::Authentication(_)) => {
            println!("Message sending failed due to authentication (expected in test)");
        }
        Err(WhatsAppError::NotConnected) => {
            println!("Message sending failed due to connection (expected in test)");
        }
        Err(e) => {
            panic!("Unexpected error sending text message: {:?}", e);
        }
    }

    // Test image message sending
    let test_image_data = vec![0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]; // PNG header
    let image_message_result = client
        .send_image_message(
            test_phone.clone(),
            test_image_data,
            Some("Test image caption".to_string()),
            "image/png".to_string(),
        )
        .await;

    match image_message_result {
        Ok(delivery_status) => {
            assert!(
                !delivery_status.message_id.is_empty(),
                "Image message ID should not be empty"
            );
            assert_eq!(
                delivery_status.recipient, test_phone,
                "Image recipient should match"
            );
            println!("Image message sent successfully: {:?}", delivery_status);
        }
        Err(WhatsAppError::Authentication(_)) | Err(WhatsAppError::NotConnected) => {
            println!("Image message sending failed due to auth/connection (expected in test)");
        }
        Err(e) => {
            panic!("Unexpected error sending image message: {:?}", e);
        }
    }

    // Test message receiving by checking event handler
    tokio::time::sleep(Duration::from_millis(100)).await;
    let events = event_handler.get_events().await;

    // In a real test environment, we might receive messages
    // For now, just verify the event handling system is working
    println!("Captured {} events during messaging test", events.len());

    // Clean up
    let _ = client.disconnect().await;
    let _ = std::fs::remove_file("test_session_messaging.json");
}

/// Test reconnection scenarios with simulated network failures
#[tokio::test]
async fn test_reconnection_scenarios() {
    // Create client with aggressive reconnection settings for testing
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(5))
        .reconnect_attempts(5)
        .session_file("test_session_reconnect.json")
        .build()
        .await
        .expect("Failed to create client");

    let event_handler = Arc::new(TestEventHandler::new());
    client.set_event_handler(event_handler.clone());

    // Initial connection
    let initial_connect = client.connect().await;
    if initial_connect.is_ok() {
        assert!(client.is_connected().await, "Should be initially connected");

        // Test graceful disconnect and reconnect
        let disconnect_result = client.disconnect().await;
        assert!(disconnect_result.is_ok(), "Disconnect should succeed");

        // Verify disconnected state
        assert!(!client.is_connected().await, "Should be disconnected");

        // Test reconnection
        let reconnect_result = client.reconnect().await;
        match reconnect_result {
            Ok(()) => {
                println!("Reconnection successful");
                assert!(client.is_connected().await, "Should be reconnected");
            }
            Err(e) => {
                println!("Reconnection failed (may be expected in test): {:?}", e);
            }
        }
    } else {
        println!("Initial connection failed (expected in test environment)");
    }

    // Test connection state tracking
    let connection_state = client.connection_state().await;
    println!("Final connection state: {:?}", connection_state);

    // Verify connection events were captured
    let events = event_handler.get_events().await;
    let connection_events: Vec<_> = events
        .iter()
        .filter(|e| matches!(e, Event::ConnectionStatusChanged(_)))
        .collect();

    println!(
        "Captured {} connection status events",
        connection_events.len()
    );

    // Clean up
    let _ = client.disconnect().await;
    let _ = std::fs::remove_file("test_session_reconnect.json");
}

/// Test session persistence and restoration functionality
#[tokio::test]
async fn test_session_persistence_and_restoration() {
    let session_file_path = "test_session_persistence.json";

    // First client - create and save session
    {
        let mut client1 = ClientBuilder::new()
            .session_file(session_file_path)
            .build()
            .await
            .expect("Failed to create first client");

        // Attempt to connect and authenticate
        let _ = client1.connect().await;
        let _ = client1.login().await;

        // Get session info if available
        if let Some(session_info) = client1.get_session_info().await {
            println!("First client session: {:?}", session_info);
        }

        // Disconnect to save session
        let _ = client1.disconnect().await;
    }

    // Verify session file was created
    let session_file_exists = std::path::Path::new(session_file_path).exists();
    if session_file_exists {
        println!("Session file was created successfully");

        // Second client - load existing session
        {
            let mut client2 = ClientBuilder::new()
                .session_file(session_file_path)
                .build()
                .await
                .expect("Failed to create second client");

            // Check if session was loaded
            if let Some(session_info) = client2.get_session_info().await {
                println!("Second client loaded session: {:?}", session_info);

                // Verify session properties
                assert!(
                    !session_info.device_id.is_empty(),
                    "Device ID should be preserved"
                );
                assert!(
                    session_info.age_seconds.is_some(),
                    "Session age should be available"
                );
            }

            // Test session validation
            let has_valid_session = client2.has_valid_session().await;
            println!("Loaded session is valid: {}", has_valid_session);

            // Test session refresh detection
            let needs_refresh = client2.session_needs_refresh().await;
            println!("Session needs refresh: {}", needs_refresh);

            // Test re-authentication detection
            let needs_reauth = client2.needs_reauthentication().await;
            println!("Needs re-authentication: {}", needs_reauth);

            // Clean up
            let _ = client2.disconnect().await;
        }
    } else {
        println!("Session file was not created (expected in test environment)");
    }

    // Test session clearing (logout)
    {
        let mut client3 = ClientBuilder::new()
            .session_file(session_file_path)
            .build()
            .await
            .expect("Failed to create third client");

        // Test logout functionality
        let logout_result = client3.logout().await;
        assert!(logout_result.is_ok(), "Logout should succeed");

        // Verify session is cleared
        let session_info_after_logout = client3.get_session_info().await;
        if let Some(info) = session_info_after_logout {
            assert!(
                !info.is_authenticated,
                "Should not be authenticated after logout"
            );
        }

        let _ = client3.disconnect().await;
    }

    // Clean up test file
    let _ = std::fs::remove_file(session_file_path);
}

/// Test error handling during authentication flow
#[tokio::test]
async fn test_authentication_error_handling() {
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(2)) // Short timeout to trigger errors
        .build()
        .await
        .expect("Failed to create client");

    let event_handler = Arc::new(TestEventHandler::new());
    client.set_event_handler(event_handler.clone());

    // Test authentication without connection
    let login_without_connection = client.login().await;
    match login_without_connection {
        Err(WhatsAppError::NotConnected) => {
            println!("Correctly detected not connected state");
        }
        Err(e) => {
            println!("Authentication failed with error (expected): {:?}", e);
        }
        Ok(()) => {
            println!("Authentication succeeded unexpectedly");
        }
    }

    // Test connection with invalid configuration
    let invalid_client_result = ClientBuilder::new()
        .server_url("invalid://url")
        .build()
        .await;

    assert!(
        invalid_client_result.is_err(),
        "Should fail with invalid server URL"
    );

    // Test timeout scenarios
    let timeout_client_result = ClientBuilder::new()
        .connect_timeout(Duration::from_millis(1)) // Very short timeout
        .build()
        .await;

    if let Ok(mut timeout_client) = timeout_client_result {
        let connect_result = timeout_client.connect().await;
        match connect_result {
            Err(WhatsAppError::Timeout { .. }) => {
                println!("Correctly handled connection timeout");
            }
            Err(e) => {
                println!("Connection failed with error (expected): {:?}", e);
            }
            Ok(()) => {
                println!("Connection succeeded unexpectedly");
                let _ = timeout_client.disconnect().await;
            }
        }
    }
}

/// Test message validation and error handling
#[tokio::test]
async fn test_message_validation_and_errors() {
    let client = ClientBuilder::new()
        .build()
        .await
        .expect("Failed to create client");

    // Test invalid phone number
    let invalid_phone_result = PhoneNumber::new("invalid_phone");
    assert!(
        invalid_phone_result.is_err(),
        "Should reject invalid phone number"
    );

    // Test valid phone number creation
    let valid_phone = PhoneNumber::new("+1234567890").expect("Should accept valid phone number");

    // Test message sending without authentication
    let send_result = client
        .send_message(valid_phone.clone(), "Test message")
        .await;
    match send_result {
        Err(WhatsAppError::Authentication(_)) => {
            println!("Correctly rejected message sending without authentication");
        }
        Err(WhatsAppError::NotConnected) => {
            println!("Correctly rejected message sending without connection");
        }
        Err(e) => {
            println!("Message sending failed with error (expected): {:?}", e);
        }
        Ok(_) => {
            panic!("Message sending should not succeed without authentication");
        }
    }

    // Test message builders
    let text_message = Message::text()
        .to(valid_phone.clone())
        .content("Test message")
        .build();
    assert!(text_message.is_ok(), "Text message builder should work");

    let image_message = Message::image()
        .to(valid_phone.clone())
        .data(vec![0x89, 0x50, 0x4E, 0x47]) // PNG header
        .mime_type("image/png")
        .caption("Test image")
        .build();
    assert!(image_message.is_ok(), "Image message builder should work");

    // Test invalid message content
    let empty_text_result = Message::text().to(valid_phone.clone()).content("").build();

    match empty_text_result {
        Err(WhatsAppError::InvalidInput(_)) => {
            println!("Correctly rejected empty message content");
        }
        _ => {
            // Empty content might be allowed, depending on validation rules
            println!("Empty content handling: {:?}", empty_text_result);
        }
    }
}

/// Test event system functionality
#[tokio::test]
async fn test_event_system() {
    let mut client = ClientBuilder::new()
        .build()
        .await
        .expect("Failed to create client");

    let event_handler = Arc::new(TestEventHandler::new());
    client.set_event_handler(event_handler.clone());

    // Test event subscription
    let mut event_receiver = client.subscribe_to_events();

    // Test manual event emission
    let test_event = Event::ConnectionStatusChanged(ConnectionStatus::Connected);
    let emit_result = client.emit_event(test_event.clone());
    assert!(emit_result.is_ok(), "Event emission should succeed");

    // Verify event was received
    let received_event = timeout(Duration::from_millis(100), event_receiver.recv()).await;
    match received_event {
        Ok(Ok(event)) => match event {
            Event::ConnectionStatusChanged(ConnectionStatus::Connected) => {
                println!("Event system working correctly");
            }
            _ => {
                println!("Received different event: {:?}", event);
            }
        },
        Ok(Err(e)) => {
            println!("Event receive error: {:?}", e);
        }
        Err(_) => {
            println!("Event receive timeout (may be expected)");
        }
    }

    // Verify event handler received the event
    tokio::time::sleep(Duration::from_millis(100)).await;
    let handler_events = event_handler.get_events().await;
    if handler_events.is_empty() {
        println!("Event handler did not receive events (may be expected in test environment)");
    } else {
        println!("Event handler received {} events", handler_events.len());
    }
}

/// Test client configuration validation
#[tokio::test]
async fn test_client_configuration_validation() {
    // Test invalid timeout
    let invalid_timeout_result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(0))
        .build()
        .await;
    assert!(
        invalid_timeout_result.is_err(),
        "Should reject zero timeout"
    );

    // Test invalid reconnect attempts
    let invalid_attempts_result = ClientBuilder::new().reconnect_attempts(0).build().await;
    assert!(
        invalid_attempts_result.is_err(),
        "Should reject zero reconnect attempts"
    );

    // Test invalid server URL
    let invalid_url_result = ClientBuilder::new()
        .server_url("not_a_websocket_url")
        .build()
        .await;
    assert!(
        invalid_url_result.is_err(),
        "Should reject invalid WebSocket URL"
    );

    // Test valid configuration
    let valid_client_result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(30))
        .reconnect_attempts(5)
        .server_url("wss://web.whatsapp.com/ws/chat")
        .session_file("test_config.json")
        .build()
        .await;
    assert!(
        valid_client_result.is_ok(),
        "Should accept valid configuration"
    );

    if let Ok(client) = valid_client_result {
        let config = client.config();
        assert_eq!(config.connect_timeout, Duration::from_secs(30));
        assert_eq!(config.reconnect_attempts, 5);
        assert_eq!(config.server_url, "wss://web.whatsapp.com/ws/chat");
        assert_eq!(
            config.session_file_path,
            Some("test_config.json".to_string())
        );
    }

    // Clean up
    let _ = std::fs::remove_file("test_config.json");
}
