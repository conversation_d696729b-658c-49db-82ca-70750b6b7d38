# WhatsApp Client Examples

This directory contains example applications demonstrating how to use the `whatsmeow-rs` library for various WhatsApp Web client operations.

## Prerequisites

Before running the examples, make sure you have:

1. **Rust installed** (1.70 or later)
2. **A WhatsApp account** on your mobile device
3. **Internet connection** for connecting to WhatsApp servers

## Running Examples

All examples can be run using Cargo:

```bash
cargo run --example <example_name>
```

## Available Examples

### 1. Basic Connection (`basic_connection.rs`)

**Purpose**: Demonstrates the fundamental connection and authentication flow.

**What it shows**:
- Creating a WhatsApp client
- Connecting to WhatsApp servers
- QR code authentication
- Basic event handling
- Graceful disconnection

**Run with**:
```bash
cargo run --example basic_connection
```

**Expected flow**:
1. C<PERSON> connects to WhatsApp servers
2. QR code is displayed in terminal
3. Scan QR code with WhatsApp mobile app (within 2 minutes)
4. Authentication completes
5. C<PERSON> remains connected until Ctrl+C

### 2. Send Message (`send_message.rs`)

**Purpose**: Shows how to send text messages with proper error handling.

**What it shows**:
- Interactive message sending
- Phone number validation
- Message delivery status tracking
- Session management
- Error handling for various scenarios

**Run with**:
```bash
cargo run --example send_message
```

**Features**:
- Interactive phone number input
- Message composition
- Delivery status reporting
- Session information display
- Persistent session storage

### 3. Message Listener (`message_listener.rs`)

**Purpose**: Demonstrates building a message bot that responds to incoming messages.

**What it shows**:
- Event-driven message handling
- Automatic message responses
- Bot command processing
- Connection status monitoring
- Advanced event handling patterns

**Run with**:
```bash
cargo run --example message_listener
```

**Bot commands**:
- `hello` - Greeting response
- `help` - Show available commands
- `time` - Get current time
- `ping` - Test bot connectivity

### 4. Error Handling (`error_handling.rs`)

**Purpose**: Comprehensive demonstration of error handling best practices.

**What it shows**:
- Different error types and handling strategies
- Retry logic with exponential backoff
- Circuit breaker patterns
- Input validation
- Recovery mechanisms
- Logging and debugging techniques

**Run with**:
```bash
cargo run --example error_handling
```

**Error scenarios covered**:
- Invalid configuration
- Connection failures
- Authentication errors
- Phone number validation
- Message sending failures
- Session management errors

## Configuration Options

### Session Persistence

Most examples use session files to persist authentication:

```rust
let client = ClientBuilder::new()
    .session_file("my_session.json")
    .build()
    .await?;
```

### Custom Timeouts

Configure connection and operation timeouts:

```rust
let client = ClientBuilder::new()
    .connect_timeout(Duration::from_secs(60))
    .reconnect_attempts(5)
    .build()
    .await?;
```

### Event Handling

Implement custom event handlers:

```rust
struct MyEventHandler;

#[async_trait::async_trait]
impl EventHandler for MyEventHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::MessageReceived(msg) => {
                println!("New message: {:?}", msg);
            }
            // Handle other events...
            _ => {}
        }
        Ok(())
    }
}
```

## Common Issues and Solutions

### QR Code Not Displaying

If the QR code doesn't display properly:
- Ensure your terminal supports Unicode characters
- Try a different terminal emulator
- Check that the `qrcode` crate is properly installed

### Connection Failures

If you can't connect to WhatsApp servers:
- Check your internet connection
- Verify firewall settings allow WebSocket connections
- Try increasing the connection timeout

### Authentication Issues

If authentication fails:
- Make sure you're scanning the QR code within 2 minutes (timeout period)
- Ensure your WhatsApp mobile app is up to date
- Try clearing the session file and re-authenticating
- If the QR code times out, simply run the example again to generate a new code

### Message Sending Failures

If messages fail to send:
- Verify you're authenticated (`client.has_valid_session()`)
- Check the phone number format (must include country code with +)
- Ensure you're connected to WhatsApp servers

## Best Practices

### Error Handling

Always handle errors appropriately:

```rust
match client.send_message(phone, "Hello").await {
    Ok(status) => println!("Message sent: {}", status.message_id),
    Err(WhatsAppError::Authentication(msg)) => {
        eprintln!("Auth error: {}", msg);
        // Re-authenticate
    }
    Err(WhatsAppError::NotConnected) => {
        eprintln!("Not connected - attempting reconnection");
        // Reconnect
    }
    Err(e) => eprintln!("Other error: {}", e),
}
```

### Resource Management

Always disconnect gracefully:

```rust
// Set up signal handler
tokio::signal::ctrl_c().await?;

// Graceful shutdown
client.disconnect().await?;
```

### Session Management

Use session persistence for better user experience:

```rust
let client = ClientBuilder::new()
    .session_file("session.json")
    .build()
    .await?;

// Session is automatically saved and restored
```

## Logging

Enable logging to debug issues:

```rust
// In your main function
tracing_subscriber::fmt::init();

// Or with specific level
tracing_subscriber::fmt()
    .with_max_level(tracing::Level::DEBUG)
    .init();
```

## Security Considerations

- **Session files**: Contain authentication credentials - keep them secure
- **Phone numbers**: Validate input to prevent injection attacks  
- **Message content**: Sanitize user input before sending
- **Error messages**: Don't expose sensitive information in error messages

## Contributing

When adding new examples:

1. Follow the existing naming convention
2. Include comprehensive documentation
3. Add error handling
4. Test with various scenarios
5. Update this README

## Support

For issues with the examples:

1. Check the error handling example for common solutions
2. Enable debug logging to see detailed error information
3. Verify your WhatsApp mobile app is working correctly
4. Check network connectivity and firewall settings

## License

These examples are provided under the same license as the main library.