//! Comprehensive error handling tests
//!
//! This module tests error scenarios and edge cases throughout the WhatsApp client system

use std::time::Duration;
use tokio::time::timeout;
use whatsmeow_rs::*;

/// Test error type classification and properties
#[test]
fn test_error_classification() {
    // Test authentication errors
    let auth_error = WhatsAppError::Authentication("Invalid credentials".to_string());
    assert!(auth_error.is_auth_error());
    assert!(!auth_error.is_connection_error());
    assert!(!auth_error.is_recoverable());
    assert_eq!(auth_error.severity(), error::ErrorSeverity::High);

    // Test connection errors
    let conn_error = WhatsAppError::NotConnected;
    assert!(!conn_error.is_auth_error());
    assert!(conn_error.is_connection_error());
    assert!(conn_error.is_recoverable());
    assert_eq!(conn_error.severity(), error::ErrorSeverity::Medium);

    // Test transient errors
    let transient_error = WhatsAppError::transient("Temporary failure", 1, 3, None);
    assert!(transient_error.is_transient());
    assert!(transient_error.is_recoverable());
    assert_eq!(transient_error.severity(), error::ErrorSeverity::Low);

    // Test rate limited errors
    let rate_error =
        WhatsAppError::rate_limited("Too many requests", Some(Duration::from_secs(30)));
    assert!(rate_error.is_rate_limited());
    assert!(rate_error.is_recoverable());
    assert_eq!(rate_error.retry_delay(), Some(Duration::from_secs(30)));
}

/// Test error user messages
#[test]
fn test_error_user_messages() {
    let auth_error = WhatsAppError::Authentication("Token expired".to_string());
    assert_eq!(
        auth_error.user_message(),
        "Authentication failed. Please try logging in again."
    );

    let not_connected_error = WhatsAppError::NotConnected;
    assert_eq!(
        not_connected_error.user_message(),
        "Not connected to WhatsApp. Please check your internet connection."
    );

    let timeout_error = WhatsAppError::Timeout {
        timeout: Duration::from_secs(30),
    };
    assert_eq!(
        timeout_error.user_message(),
        "Operation timed out. Please check your internet connection and try again."
    );

    let rate_limited_error =
        WhatsAppError::rate_limited("Too many requests", Some(Duration::from_secs(60)));
    assert!(rate_limited_error.user_message().contains("Rate limited"));
    assert!(rate_limited_error.user_message().contains("60s"));
}

/// Test client builder validation errors
#[tokio::test]
async fn test_client_builder_validation_errors() {
    // Test zero timeout
    let zero_timeout_result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(0))
        .build()
        .await;

    assert!(zero_timeout_result.is_err());
    if let Err(error) = zero_timeout_result {
        assert!(matches!(error, WhatsAppError::InvalidConfig(_)));
        assert!(error.to_string().contains("timeout"));
    }

    // Test zero reconnect attempts
    let zero_attempts_result = ClientBuilder::new().reconnect_attempts(0).build().await;

    assert!(zero_attempts_result.is_err());
    if let Err(error) = zero_attempts_result {
        assert!(matches!(error, WhatsAppError::InvalidConfig(_)));
        assert!(
            error
                .to_string()
                .contains("Reconnect attempts must be greater than zero")
        );
    }

    // Test invalid server URL
    let invalid_url_result = ClientBuilder::new()
        .server_url("not-a-websocket-url")
        .build()
        .await;

    assert!(invalid_url_result.is_err());
    if let Err(error) = invalid_url_result {
        assert!(matches!(error, WhatsAppError::InvalidConfig(_)));
        assert!(error.to_string().contains("URL"));
    }

    // Test too large timeout
    let large_timeout_result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(400)) // > 5 minutes
        .build()
        .await;

    assert!(large_timeout_result.is_err());
    if let Err(error) = large_timeout_result {
        assert!(matches!(error, WhatsAppError::InvalidConfig(_)));
    }

    // Test too many reconnect attempts
    let too_many_attempts_result = ClientBuilder::new()
        .reconnect_attempts(150) // > 100
        .build()
        .await;

    assert!(too_many_attempts_result.is_err());
    if let Err(error) = too_many_attempts_result {
        assert!(matches!(error, WhatsAppError::InvalidConfig(_)));
    }
}

/// Test phone number validation errors
#[test]
fn test_phone_number_validation_errors() {
    // Empty phone number
    let empty_result = PhoneNumber::new("");
    assert!(empty_result.is_err());
    if let Err(error) = empty_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("Invalid phone number format"));
    }

    // Missing country code
    let no_plus_result = PhoneNumber::new("1234567890");
    assert!(no_plus_result.is_err());
    if let Err(error) = no_plus_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("E.164"));
    }

    // Too short
    let too_short_result = PhoneNumber::new("+123");
    assert!(too_short_result.is_err());
    if let Err(error) = too_short_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("7-15 digits"));
    }

    // Too long
    let too_long_result = PhoneNumber::new("+1234567890123456789");
    assert!(too_long_result.is_err());
    if let Err(error) = too_long_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("7-15 digits"));
    }

    // Invalid characters
    let invalid_chars_result = PhoneNumber::new("+123abc7890");
    assert!(invalid_chars_result.is_err());
    if let Err(error) = invalid_chars_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("E.164"));
    }
}

/// Test message validation errors
#[test]
fn test_message_validation_errors() {
    let valid_phone = PhoneNumber::new("+1234567890").unwrap();

    // Test text message without recipient
    let no_recipient_result = Message::text().content("Hello").build();
    assert!(no_recipient_result.is_err());
    if let Err(error) = no_recipient_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("Recipient"));
    }

    // Test text message without content
    let no_content_result = Message::text().to(valid_phone.clone()).build();
    assert!(no_content_result.is_err());
    if let Err(error) = no_content_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("content"));
    }

    // Test image message without data
    let no_data_result = Message::image()
        .to(valid_phone.clone())
        .mime_type("image/jpeg")
        .build();
    assert!(no_data_result.is_err());
    if let Err(error) = no_data_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("data"));
    }

    // Test image message without MIME type
    let no_mime_result = Message::image()
        .to(valid_phone.clone())
        .data(vec![0x89, 0x50, 0x4E, 0x47])
        .build();
    assert!(no_mime_result.is_err());
    if let Err(error) = no_mime_result {
        assert!(matches!(error, WhatsAppError::InvalidInput(_)));
        assert!(error.to_string().contains("MIME"));
    }

    // Test very long text message
    let long_text = "a".repeat(70000); // > 65536 characters
    let long_message_result = Message::text()
        .to(valid_phone.clone())
        .content(long_text)
        .build();

    // This might be allowed or rejected depending on validation rules
    match long_message_result {
        Err(WhatsAppError::InvalidInput(_)) => {
            println!("Long message correctly rejected");
        }
        Ok(_) => {
            println!("Long message allowed (validation may be lenient)");
        }
        Err(e) => {
            panic!("Unexpected error for long message: {:?}", e);
        }
    }

    // Test empty image data
    let empty_image_result = Message::image()
        .to(valid_phone.clone())
        .data(vec![])
        .mime_type("image/jpeg")
        .build();

    match empty_image_result {
        Err(WhatsAppError::InvalidInput(_)) => {
            println!("Empty image data correctly rejected");
        }
        Ok(_) => {
            println!("Empty image data allowed");
        }
        Err(e) => {
            panic!("Unexpected error for empty image: {:?}", e);
        }
    }
}

/// Test connection timeout scenarios
#[tokio::test]
async fn test_connection_timeout_scenarios() {
    // Test very short timeout
    let short_timeout_client = ClientBuilder::new()
        .connect_timeout(Duration::from_millis(1))
        .build()
        .await;

    if let Ok(mut client) = short_timeout_client {
        let connect_result = timeout(Duration::from_secs(5), client.connect()).await;

        match connect_result {
            Ok(Err(WhatsAppError::Timeout { timeout: t })) => {
                assert_eq!(t, Duration::from_millis(1));
                println!("Connection timeout correctly detected");
            }
            Ok(Err(e)) => {
                println!("Connection failed with different error (expected): {:?}", e);
            }
            Ok(Ok(())) => {
                println!("Connection succeeded unexpectedly");
                let _ = client.disconnect().await;
            }
            Err(_) => {
                println!("Test timeout (connection attempt took too long)");
            }
        }
    }
}

/// Test authentication error scenarios
#[tokio::test]
async fn test_authentication_error_scenarios() {
    let mut client = ClientBuilder::new()
        .build()
        .await
        .expect("Failed to create client");

    // Test login without connection
    let login_without_connection = client.login().await;
    match login_without_connection {
        Err(WhatsAppError::NotConnected) => {
            println!("Correctly detected not connected state");
        }
        Err(e) => {
            println!("Login failed with error (expected): {:?}", e);
        }
        Ok(()) => {
            println!("Login succeeded unexpectedly");
        }
    }

    // Test message sending without authentication
    let phone = PhoneNumber::new("+1234567890").unwrap();
    let send_without_auth = client.send_message(phone, "Test").await;

    match send_without_auth {
        Err(WhatsAppError::Authentication(_)) => {
            println!("Correctly rejected message sending without authentication");
        }
        Err(WhatsAppError::NotConnected) => {
            println!("Correctly rejected message sending without connection");
        }
        Err(e) => {
            println!("Message sending failed with error (expected): {:?}", e);
        }
        Ok(_) => {
            panic!("Message sending should not succeed without authentication");
        }
    }
}

/// Test session error scenarios
#[tokio::test]
async fn test_session_error_scenarios() {
    // Test loading invalid session file
    let invalid_session_path = "nonexistent_session.json";
    let client_with_invalid_session = ClientBuilder::new()
        .session_file(invalid_session_path)
        .build()
        .await;

    // Should succeed (missing session file is not an error)
    assert!(client_with_invalid_session.is_ok());

    if let Ok(client) = client_with_invalid_session {
        // Session should not be valid
        assert!(!client.has_valid_session().await);

        // Session info should be None
        assert!(client.get_session_info().await.is_none());
    }

    // Test session file in non-existent directory
    let nested_session_path = "nonexistent_dir/session.json";
    let client_with_nested_session = ClientBuilder::new()
        .session_file(nested_session_path)
        .build()
        .await;

    // Should succeed (directory will be created)
    assert!(client_with_nested_session.is_ok());

    // Clean up
    let _ = std::fs::remove_file(nested_session_path);
    let _ = std::fs::remove_dir("nonexistent_dir");
}

/// Test network error simulation
#[tokio::test]
async fn test_network_error_simulation() {
    // Test with invalid server URL that will cause network errors
    let client_result = ClientBuilder::new()
        .server_url("wss://invalid.nonexistent.domain.example/ws")
        .connect_timeout(Duration::from_secs(2))
        .build()
        .await;

    if let Ok(mut client) = client_result {
        let connect_result = client.connect().await;

        match connect_result {
            Err(WhatsAppError::Connection(_)) => {
                println!("Network error correctly detected");
            }
            Err(WhatsAppError::Timeout { .. }) => {
                println!("Connection timeout (expected for invalid domain)");
            }
            Err(e) => {
                println!("Connection failed with error (expected): {:?}", e);
            }
            Ok(()) => {
                println!("Connection succeeded unexpectedly");
                let _ = client.disconnect().await;
            }
        }
    }
}

/// Test error logging functionality
#[test]
fn test_error_logging() {
    // Test different error severities
    let high_severity_error = WhatsAppError::Authentication("Critical auth failure".to_string());
    assert_eq!(high_severity_error.severity(), error::ErrorSeverity::High);

    let medium_severity_error = WhatsAppError::NotConnected;
    assert_eq!(
        medium_severity_error.severity(),
        error::ErrorSeverity::Medium
    );

    let low_severity_error = WhatsAppError::InvalidInput("Minor input issue".to_string());
    assert_eq!(low_severity_error.severity(), error::ErrorSeverity::Low);

    // Test error logging (this would normally write to logs)
    high_severity_error.log();
    medium_severity_error.log();
    low_severity_error.log();

    // Test error logging with context
    high_severity_error.log_with_context("Authentication flow");
    medium_severity_error.log_with_context("Connection management");
    low_severity_error.log_with_context("Input validation");
}

/// Test error chain and context
#[test]
fn test_error_chain_and_context() {
    let _root_cause = WhatsAppError::Network("DNS resolution failed".to_string());
    let intermediate_error = WhatsAppError::Connection(tokio_tungstenite::tungstenite::Error::Io(
        std::io::Error::new(std::io::ErrorKind::Other, "Connection refused"),
    ));
    let operation_error = WhatsAppError::operation(
        "establish_connection",
        "Failed to connect to WhatsApp servers",
        intermediate_error,
    );

    // Test error chain
    assert!(operation_error.to_string().contains("establish_connection"));
    assert!(operation_error.to_string().contains("Failed to connect"));

    // Test source chain
    let mut current_error: &dyn std::error::Error = &operation_error;
    let mut chain_length = 0;

    while let Some(source) = current_error.source() {
        current_error = source;
        chain_length += 1;
        if chain_length > 10 {
            break; // Prevent infinite loops
        }
    }

    assert!(chain_length > 0, "Error should have a source chain");
}

/// Test concurrent error handling
#[tokio::test]
async fn test_concurrent_error_handling() {
    use std::sync::Arc;

    let client = Arc::new(
        ClientBuilder::new()
            .build()
            .await
            .expect("Failed to create client"),
    );

    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Spawn multiple concurrent operations that should fail
    let mut handles = Vec::new();

    for i in 0..10 {
        let client_clone = client.clone();
        let phone_clone = phone.clone();
        let handle = tokio::spawn(async move {
            client_clone
                .send_message(phone_clone, &format!("Message {}", i))
                .await
        });
        handles.push(handle);
    }

    // Wait for all operations to complete
    let mut error_count = 0;
    for handle in handles {
        match handle.await {
            Ok(Err(_)) => error_count += 1,
            Ok(Ok(_)) => {
                // Unexpected success
                println!("Unexpected message send success");
            }
            Err(e) => {
                println!("Task join error: {:?}", e);
            }
        }
    }

    // All operations should have failed (not authenticated)
    assert_eq!(
        error_count, 10,
        "All concurrent operations should have failed"
    );
}

/// Test error recovery scenarios
#[tokio::test]
async fn test_error_recovery_scenarios() {
    use std::sync::Arc;
    use std::sync::atomic::{AtomicU32, Ordering};

    // Test retry logic with eventual success
    let attempt_counter = Arc::new(AtomicU32::new(0));
    let counter_clone = attempt_counter.clone();

    let operation = || {
        let counter = counter_clone.clone();
        async move {
            let attempt = counter.fetch_add(1, Ordering::Relaxed);
            if attempt < 2 {
                Err(WhatsAppError::Network(
                    "Temporary network issue".to_string(),
                ))
            } else {
                Ok("Success after retries")
            }
        }
    };

    // This would use the recovery system in a real implementation
    // For now, we simulate the retry logic
    let max_attempts = 5;

    for attempt in 0..max_attempts {
        match operation().await {
            Ok(result) => {
                assert_eq!(result, "Success after retries");
                assert_eq!(attempt_counter.load(Ordering::Relaxed), 3);
                return;
            }
            Err(_error) => {
                if attempt < max_attempts - 1 {
                    tokio::time::sleep(Duration::from_millis(10)).await;
                }
            }
        }
    }

    panic!("Operation should have succeeded after retries");
}

/// Test memory safety in error handling
#[test]
fn test_error_memory_safety() {
    // Test that errors can be cloned and moved safely
    let original_error = WhatsAppError::Authentication("Test error".to_string());
    let cloned_error = original_error
        .try_clone()
        .expect("Should be able to clone authentication error");

    // Move the original error
    let moved_error = original_error;

    // Both errors should be valid
    assert!(moved_error.is_auth_error());
    assert!(cloned_error.is_auth_error());

    // Test error in different thread
    let thread_handle = std::thread::spawn(move || moved_error.user_message());

    let user_message = thread_handle.join().unwrap();
    assert!(!user_message.is_empty());
}
