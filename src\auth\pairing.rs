//! Device pairing logic

use crate::auth::QRGenerator;
use crate::client::session::Session;
use crate::error::{Result, WhatsAppError};
use base64::{Engine as _, engine::general_purpose};
use rand::RngCore;
use sha2::Digest;

/// Manages device pairing process
pub struct PairingManager {
    noise_state: NoiseState,
    client_id: String,
}

/// Noise protocol state for secure handshake
pub struct NoiseState {
    /// Handshake state
    pub handshake_hash: Vec<u8>,
    /// Shared secret after handshake
    pub shared_secret: Option<Vec<u8>>,
    /// Local keypair
    pub local_keypair: (Vec<u8>, Vec<u8>), // (private, public)
    /// Remote public key
    pub remote_public_key: Option<Vec<u8>>,
}

impl NoiseState {
    /// Create new noise state
    pub fn new() -> Self {
        let mut rng = rand::rng();
        let mut private_key = vec![0u8; 32];
        let mut public_key = vec![0u8; 32];
        rng.fill_bytes(&mut private_key);
        rng.fill_bytes(&mut public_key);

        Self {
            handshake_hash: Vec::new(),
            shared_secret: None,
            local_keypair: (private_key, public_key),
            remote_public_key: None,
        }
    }

    /// Perform handshake step
    pub fn perform_handshake(&mut self, remote_data: &[u8]) -> Result<Vec<u8>> {
        // Store remote public key
        if remote_data.len() >= 32 {
            self.remote_public_key = Some(remote_data[..32].to_vec());
        }

        // Simulate handshake hash computation
        let mut handshake_data = Vec::new();
        handshake_data.extend_from_slice(&self.local_keypair.1); // local public key
        handshake_data.extend_from_slice(remote_data);

        // Simple hash simulation (in real implementation, this would use proper Noise protocol)
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(&handshake_data);
        self.handshake_hash = hasher.finalize().to_vec();

        // Generate shared secret
        let mut shared_secret = vec![0u8; 32];
        let mut rng = rand::rng();
        rng.fill_bytes(&mut shared_secret);
        self.shared_secret = Some(shared_secret.clone());

        Ok(shared_secret)
    }
}

impl PairingManager {
    /// Create a new pairing manager
    pub fn new() -> Self {
        let mut rng = rand::rng();
        let mut client_id_bytes = [0u8; 16];
        rng.fill_bytes(&mut client_id_bytes);
        let client_id = general_purpose::STANDARD.encode(client_id_bytes);

        Self {
            noise_state: NoiseState::new(),
            client_id,
        }
    }

    /// Initiate pairing process by generating QR code keys
    pub async fn initiate_pairing(&mut self) -> Result<QRGenerator> {
        // Create QR generator with our keys
        let mut qr_generator = QRGenerator::new();

        // Replace the random noise key with our noise protocol public key
        qr_generator.noise_key = self.noise_state.local_keypair.1.clone();

        Ok(qr_generator)
    }

    /// Complete pairing process after QR code scan
    pub async fn complete_pairing(&mut self, scan_result: Vec<u8>) -> Result<Session> {
        if scan_result.is_empty() {
            return Err(WhatsAppError::Authentication(
                "Empty scan result received".to_string(),
            ));
        }

        // Perform noise handshake with scan result
        let shared_secret = self
            .noise_state
            .perform_handshake(&scan_result)
            .map_err(|e| WhatsAppError::Authentication(format!("Handshake failed: {}", e)))?;

        // Extract authentication tokens from scan result
        // In real implementation, this would parse the actual WhatsApp protocol response
        let (device_id, client_token, server_token) = self.extract_tokens(&scan_result)?;

        // Create session with authentication data
        let mut session = Session::with_device_id(device_id);
        session.set_authenticated(client_token, server_token);

        // Set encryption keys
        let encryption_keys = crate::client::session::EncryptionKeys {
            noise_key: shared_secret,
            identity_key: self.generate_identity_key()?,
            signed_pre_key: self.generate_signed_pre_key()?,
            registration_id: self.generate_registration_id(),
        };
        session.set_encryption_keys(encryption_keys);

        Ok(session)
    }

    /// Extract authentication tokens from scan result
    fn extract_tokens(&self, scan_result: &[u8]) -> Result<(String, String, String)> {
        // In a real implementation, this would parse the actual protocol response
        // For now, we'll simulate token extraction

        if scan_result.len() < 64 {
            return Err(WhatsAppError::Authentication(
                "Invalid scan result: insufficient data".to_string(),
            ));
        }

        // Generate device ID from client ID and scan data
        let mut hasher = sha2::Sha256::new();
        hasher.update(&self.client_id);
        hasher.update(&scan_result[..32]);
        let device_hash = hasher.finalize();
        let device_id = general_purpose::STANDARD.encode(&device_hash[..16]);

        // Generate client token
        let mut hasher = sha2::Sha256::new();
        hasher.update(b"client_token");
        hasher.update(&scan_result[32..64]);
        let client_hash = hasher.finalize();
        let client_token = general_purpose::STANDARD.encode(&client_hash[..24]);

        // Generate server token
        let mut hasher = sha2::Sha256::new();
        hasher.update(b"server_token");
        hasher.update(&self.noise_state.handshake_hash);
        let server_hash = hasher.finalize();
        let server_token = general_purpose::STANDARD.encode(&server_hash[..24]);

        Ok((device_id, client_token, server_token))
    }

    /// Generate MAC key for message authentication
    pub fn generate_mac_key(&self) -> Result<Vec<u8>> {
        self.derive_key(b"mac_key")
    }

    /// Generate encryption key for message encryption
    pub fn generate_enc_key(&self) -> Result<Vec<u8>> {
        self.derive_key(b"enc_key")
    }

    /// Common key derivation function
    fn derive_key(&self, purpose: &[u8]) -> Result<Vec<u8>> {
        let shared_secret = self.noise_state.shared_secret.as_ref().ok_or_else(|| {
            WhatsAppError::Authentication("No shared secret available".to_string())
        })?;

        let mut hasher = sha2::Sha256::new();
        hasher.update(purpose);
        hasher.update(shared_secret);
        Ok(hasher.finalize()[..32].to_vec())
    }

    /// Generate identity key for Signal protocol
    fn generate_identity_key(&self) -> Result<Vec<u8>> {
        let mut key_data = Vec::new();
        key_data.extend_from_slice(self.client_id.as_bytes());
        self.derive_key_with_data(b"identity_key", &key_data)
    }

    /// Generate signed pre key for Signal protocol
    fn generate_signed_pre_key(&self) -> Result<Vec<u8>> {
        self.derive_key_with_data(b"signed_pre_key", &self.noise_state.local_keypair.1)
    }

    /// Derive key with additional data
    fn derive_key_with_data(&self, purpose: &[u8], additional_data: &[u8]) -> Result<Vec<u8>> {
        let shared_secret = self.noise_state.shared_secret.as_ref().ok_or_else(|| {
            WhatsAppError::Authentication("No shared secret available".to_string())
        })?;

        let mut hasher = sha2::Sha256::new();
        hasher.update(purpose);
        hasher.update(shared_secret);
        hasher.update(additional_data);
        Ok(hasher.finalize()[..32].to_vec())
    }

    /// Generate registration ID for Signal protocol
    fn generate_registration_id(&self) -> u32 {
        let mut hasher = sha2::Sha256::new();
        hasher.update(b"registration_id");
        hasher.update(&self.client_id);
        hasher.update(&self.noise_state.local_keypair.0); // private key
        let hash = hasher.finalize();

        // Convert first 4 bytes to u32
        u32::from_be_bytes([hash[0], hash[1], hash[2], hash[3]])
    }
}

impl Default for PairingManager {
    fn default() -> Self {
        Self::new()
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_noise_state_new() {
        let noise_state = NoiseState::new();

        // Check that keys are generated
        assert_eq!(noise_state.local_keypair.0.len(), 32); // private key
        assert_eq!(noise_state.local_keypair.1.len(), 32); // public key
        assert!(noise_state.handshake_hash.is_empty());
        assert!(noise_state.shared_secret.is_none());
        assert!(noise_state.remote_public_key.is_none());
    }

    #[test]
    fn test_noise_state_perform_handshake() {
        let mut noise_state = NoiseState::new();
        let remote_data = vec![1u8; 64]; // Simulate remote handshake data

        let result = noise_state.perform_handshake(&remote_data);
        assert!(result.is_ok());

        let shared_secret = result.unwrap();
        assert_eq!(shared_secret.len(), 32);
        assert!(noise_state.shared_secret.is_some());
        assert!(noise_state.remote_public_key.is_some());
        assert!(!noise_state.handshake_hash.is_empty());
        assert_eq!(noise_state.remote_public_key.as_ref().unwrap().len(), 32);
    }

    #[test]
    fn test_noise_state_perform_handshake_short_data() {
        let mut noise_state = NoiseState::new();
        let remote_data = vec![1u8; 16]; // Short data

        let result = noise_state.perform_handshake(&remote_data);
        assert!(result.is_ok());

        // Should still work but remote_public_key should be None
        assert!(noise_state.shared_secret.is_some());
        assert!(noise_state.remote_public_key.is_none());
    }

    #[test]
    fn test_pairing_manager_new() {
        let manager = PairingManager::new();

        // Check that client_id is generated
        assert!(!manager.client_id.is_empty());

        // Check that noise state is initialized
        assert_eq!(manager.noise_state.local_keypair.0.len(), 32);
        assert_eq!(manager.noise_state.local_keypair.1.len(), 32);
    }

    #[test]
    fn test_pairing_manager_uniqueness() {
        let manager1 = PairingManager::new();
        let manager2 = PairingManager::new();

        // Each manager should have unique client IDs and keys
        assert_ne!(manager1.client_id, manager2.client_id);
        assert_ne!(
            manager1.noise_state.local_keypair.0,
            manager2.noise_state.local_keypair.0
        );
        assert_ne!(
            manager1.noise_state.local_keypair.1,
            manager2.noise_state.local_keypair.1
        );
    }

    #[tokio::test]
    async fn test_initiate_pairing() {
        let mut manager = PairingManager::new();

        let result = manager.initiate_pairing().await;
        assert!(result.is_ok());

        let qr_generator = result.unwrap();

        // Check that QR generator has our noise key
        assert_eq!(qr_generator.noise_key, manager.noise_state.local_keypair.1);

        // Check that QR data can be generated with a test ref
        let test_ref = "2@test_ref_from_server";
        let qr_data = qr_generator.generate_qr_data(test_ref);
        assert!(!qr_data.is_empty());
        assert!(qr_data.contains(','));
        assert!(qr_data.starts_with(test_ref));
    }

    #[tokio::test]
    async fn test_complete_pairing_success() {
        let mut manager = PairingManager::new();
        let scan_result = vec![1u8; 128]; // Sufficient data for pairing

        let result = manager.complete_pairing(scan_result).await;
        assert!(result.is_ok());

        let session = result.unwrap();

        // Check that session is properly created
        assert!(!session.device_id.is_empty());
        assert!(!session.client_token.is_empty());
        assert!(!session.server_token.is_empty());
        assert_eq!(session.encryption_keys.noise_key.len(), 32);
        assert_eq!(session.encryption_keys.identity_key.len(), 32);
        assert_eq!(session.encryption_keys.signed_pre_key.len(), 32);
        assert!(session.encryption_keys.registration_id > 0);
    }

    #[tokio::test]
    async fn test_complete_pairing_empty_scan_result() {
        let mut manager = PairingManager::new();
        let scan_result = vec![]; // Empty scan result

        let result = manager.complete_pairing(scan_result).await;
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::Authentication(msg) => {
                assert!(msg.contains("Empty scan result"));
            }
            _ => panic!("Expected Authentication error"),
        }
    }

    #[tokio::test]
    async fn test_complete_pairing_insufficient_data() {
        let mut manager = PairingManager::new();
        let scan_result = vec![1u8; 32]; // Insufficient data

        let result = manager.complete_pairing(scan_result).await;
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::Authentication(msg) => {
                assert!(msg.contains("insufficient data"));
            }
            _ => panic!("Expected Authentication error"),
        }
    }

    #[test]
    fn test_extract_tokens() {
        let manager = PairingManager::new();
        let scan_result = vec![1u8; 128];

        let result = manager.extract_tokens(&scan_result);
        assert!(result.is_ok());

        let (device_id, client_token, server_token) = result.unwrap();
        assert!(!device_id.is_empty());
        assert!(!client_token.is_empty());
        assert!(!server_token.is_empty());

        // Tokens should be different
        assert_ne!(device_id, client_token);
        assert_ne!(client_token, server_token);
        assert_ne!(device_id, server_token);
    }

    #[test]
    fn test_extract_tokens_insufficient_data() {
        let manager = PairingManager::new();
        let scan_result = vec![1u8; 32]; // Less than 64 bytes

        let result = manager.extract_tokens(&scan_result);
        assert!(result.is_err());
    }

    #[test]
    fn test_generate_mac_key() {
        let mut manager = PairingManager::new();

        // First set up shared secret
        let remote_data = vec![1u8; 64];
        manager.noise_state.perform_handshake(&remote_data).unwrap();

        let result = manager.generate_mac_key();
        assert!(result.is_ok());

        let mac_key = result.unwrap();
        assert_eq!(mac_key.len(), 32);
    }

    #[test]
    fn test_generate_enc_key() {
        let mut manager = PairingManager::new();

        // First set up shared secret
        let remote_data = vec![1u8; 64];
        manager.noise_state.perform_handshake(&remote_data).unwrap();

        let result = manager.generate_enc_key();
        assert!(result.is_ok());

        let enc_key = result.unwrap();
        assert_eq!(enc_key.len(), 32);
    }

    #[test]
    fn test_generate_keys_without_shared_secret() {
        let manager = PairingManager::new();

        // Should fail without shared secret
        let mac_result = manager.generate_mac_key();
        assert!(mac_result.is_err());

        let enc_result = manager.generate_enc_key();
        assert!(enc_result.is_err());
    }

    #[test]
    fn test_default_implementation() {
        let manager = PairingManager::default();

        // Default should work the same as new()
        assert!(!manager.client_id.is_empty());
        assert_eq!(manager.noise_state.local_keypair.0.len(), 32);
        assert_eq!(manager.noise_state.local_keypair.1.len(), 32);
    }

    #[tokio::test]
    async fn test_full_pairing_flow() {
        let mut manager = PairingManager::new();

        // Step 1: Initiate pairing
        let qr_generator = manager.initiate_pairing().await.unwrap();
        let test_ref = "2@test_full_flow_ref";
        assert!(!qr_generator.generate_qr_data(test_ref).is_empty());

        // Step 2: Complete pairing with simulated scan result
        let scan_result = vec![2u8; 128];
        let session = manager.complete_pairing(scan_result).await.unwrap();

        // Verify session is complete and valid
        assert!(!session.device_id.is_empty());
        assert!(!session.client_token.is_empty());
        assert!(!session.server_token.is_empty());
        assert_eq!(session.encryption_keys.noise_key.len(), 32);
        assert_eq!(session.encryption_keys.identity_key.len(), 32);
        assert_eq!(session.encryption_keys.signed_pre_key.len(), 32);
        assert!(session.encryption_keys.registration_id > 0);
    }
}
