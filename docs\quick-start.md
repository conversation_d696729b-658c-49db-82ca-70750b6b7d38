# Quick Start Guide

## Basic Usage

```rust
use whatsmeow_rs::{WhatsAppClient, PhoneNumber, Result};

#[tokio::main]
async fn main() -> Result<()> {
    // Create client with default configuration
    let mut client = WhatsAppClient::new().await?;
    
    // Connect and authenticate
    client.connect().await?;
    client.login().await?; // This will display a QR code to scan (2-minute timeout)
    
    // Send a message
    let phone = PhoneNumber::new("+1234567890")?;
    let status = client.send_message(phone, "Hello from Rust!").await?;
    println!("Message sent with ID: {}", status.message_id);
    
    // Gracefully disconnect
    client.disconnect().await?;
    Ok(())
}
```

## QR Code Authentication

When you call `client.login().await?`, the library will:

1. Generate a QR code and display it in the terminal
2. Wait for up to 2 minutes for you to scan the code with WhatsApp on your mobile device
3. Complete the authentication process once the code is scanned

Make sure to have WhatsApp installed on your mobile device and be ready to scan the QR code within the 2-minute timeout window.

## Dependencies

Add these to your `Cargo.toml`:

```toml
[dependencies]
whatsmeow-rs = "0.1"
tokio = { version = "1.0", features = ["full"] }
```