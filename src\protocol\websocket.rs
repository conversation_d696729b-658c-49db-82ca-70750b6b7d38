//! Production WebSocket message processing for WhatsApp Web protocol

use crate::error::Result;
use crate::types::Event;
use prost::Message;
use std::collections::HashMap;
use tokio::sync::broadcast;

/// WebSocket message types in WhatsApp Web protocol
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum WSMessageType {
    /// Initial handshake messages
    Handshake,
    /// Authentication-related messages
    Auth,
    /// Regular chat messages
    Message,
    /// Presence updates
    Presence,
    /// Media messages
    Media,
    /// System notifications
    System,
    /// Unknown message type
    Unknown,
}

/// WebSocket frame processor for WhatsApp Web protocol
pub struct WSFrameProcessor {
    event_sender: broadcast::Sender<Event>,
    message_handlers: HashMap<WSMessageType, Box<dyn MessageHandler + Send + Sync>>,
}

impl WSFrameProcessor {
    /// Create a new WebSocket frame processor
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        let mut processor = Self {
            event_sender,
            message_handlers: HashMap::new(),
        };

        // Register default message handlers
        processor.register_default_handlers();
        processor
    }

    /// Process incoming WebSocket frame
    pub async fn process_frame(&self, frame_data: Vec<u8>) -> Result<Option<Event>> {
        // Determine message type from frame
        let message_type = self.determine_message_type(&frame_data)?;

        // Get appropriate handler
        if let Some(handler) = self.message_handlers.get(&message_type) {
            handler.handle_message(frame_data).await
        } else {
            tracing::debug!("No handler for message type: {:?}", message_type);
            Ok(None)
        }
    }

    /// Register a custom message handler
    pub fn register_handler(
        &mut self,
        msg_type: WSMessageType,
        handler: Box<dyn MessageHandler + Send + Sync>,
    ) {
        self.message_handlers.insert(msg_type, handler);
    }

    /// Register default message handlers
    fn register_default_handlers(&mut self) {
        self.message_handlers.insert(
            WSMessageType::Handshake,
            Box::new(HandshakeHandler::new(self.event_sender.clone())),
        );

        self.message_handlers.insert(
            WSMessageType::Auth,
            Box::new(AuthHandler::new(self.event_sender.clone())),
        );

        self.message_handlers.insert(
            WSMessageType::Message,
            Box::new(ChatMessageHandler::new(self.event_sender.clone())),
        );

        self.message_handlers.insert(
            WSMessageType::System,
            Box::new(SystemHandler::new(self.event_sender.clone())),
        );
    }

    /// Determine message type from frame data
    fn determine_message_type(&self, frame_data: &[u8]) -> Result<WSMessageType> {
        if frame_data.len() < 3 {
            return Ok(WSMessageType::Unknown);
        }

        // WhatsApp Web protocol uses binary frames with tag-length-value structure
        let tag = frame_data[0];
        let length = u16::from_be_bytes([frame_data[1], frame_data[2]]) as usize;

        if frame_data.len() < 3 + length {
            return Ok(WSMessageType::Unknown);
        }

        match tag {
            // Handshake and authentication messages
            0x00 => Ok(WSMessageType::Handshake),
            0x01..=0x03 => Ok(WSMessageType::Auth),
            // Chat messages (WebMessageInfo)
            0x04 => {
                let payload = &frame_data[3..3 + length];
                if self.is_web_message_info(payload) {
                    Ok(WSMessageType::Message)
                } else {
                    Ok(WSMessageType::Unknown)
                }
            }
            // Presence and system messages
            0x05 => Ok(WSMessageType::Presence),
            0x06 => Ok(WSMessageType::System),
            // Media messages
            0x07 => Ok(WSMessageType::Media),
            _ => Ok(WSMessageType::Unknown),
        }
    }

    /// Check if payload is a WebMessageInfo protobuf
    fn is_web_message_info(&self, payload: &[u8]) -> bool {
        use prost::Message;
        crate::protocol::proto::WebMessageInfo::decode(payload).is_ok()
    }
}

/// Trait for handling specific message types
#[async_trait::async_trait]
pub trait MessageHandler {
    async fn handle_message(&self, frame_data: Vec<u8>) -> Result<Option<Event>>;
}

/// Handler for handshake messages
pub struct HandshakeHandler {
    event_sender: broadcast::Sender<Event>,
}

impl HandshakeHandler {
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        Self { event_sender }
    }
}

#[async_trait::async_trait]
impl MessageHandler for HandshakeHandler {
    async fn handle_message(&self, frame_data: Vec<u8>) -> Result<Option<Event>> {
        tracing::debug!("Processing handshake message ({} bytes)", frame_data.len());

        // Try to parse as handshake message
        if let Ok(handshake) =
            crate::protocol::proto::HandshakeMessage::decode(frame_data.as_slice())
        {
            if handshake.server_hello.is_some() {
                tracing::info!("Received server hello in handshake");
                // Server hello received - handshake is progressing
                return Ok(Some(Event::ConnectionStatusChanged(
                    crate::types::ConnectionStatus::Connected,
                )));
            }
        }

        Ok(None)
    }
}

/// Handler for authentication messages
pub struct AuthHandler {
    event_sender: broadcast::Sender<Event>,
}

impl AuthHandler {
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        Self { event_sender }
    }
}

#[async_trait::async_trait]
impl MessageHandler for AuthHandler {
    async fn handle_message(&self, frame_data: Vec<u8>) -> Result<Option<Event>> {
        tracing::debug!("Processing auth message ({} bytes)", frame_data.len());

        if frame_data.len() < 3 {
            return Ok(None);
        }

        let tag = frame_data[0];
        let length = u16::from_be_bytes([frame_data[1], frame_data[2]]) as usize;

        if frame_data.len() < 3 + length {
            return Ok(None);
        }

        let payload = &frame_data[3..3 + length];

        match tag {
            // QR scan confirmation
            0x02 => {
                if payload.len() >= 32 && payload.get(32) == Some(&0x01) {
                    tracing::info!("QR code scan detected from server");
                    return Ok(Some(Event::LoginSuccess));
                }
            }
            // Authentication result
            0x03 => {
                if !payload.is_empty() {
                    match payload[0] {
                        0x01 => {
                            tracing::info!("Authentication successful");
                            return Ok(Some(Event::LoginSuccess));
                        }
                        0x02 => {
                            let reason = if payload.len() > 1 {
                                String::from_utf8_lossy(&payload[1..]).to_string()
                            } else {
                                "Authentication failed".to_string()
                            };

                            tracing::warn!("Authentication failed: {}", reason);
                            return Ok(Some(Event::LoginFailure(crate::types::LoginError {
                                reason: crate::types::LoginFailureReason::AuthenticationTimeout,
                                message: reason,
                                retry_after: Some(std::time::Duration::from_secs(30)),
                            })));
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }

        Ok(None)
    }
}

/// Handler for chat messages
pub struct ChatMessageHandler {
    event_sender: broadcast::Sender<Event>,
}

impl ChatMessageHandler {
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        Self { event_sender }
    }
}

impl ChatMessageHandler {
    fn convert_to_incoming_message(
        &self,
        web_message: crate::protocol::proto::WebMessageInfo,
        message: crate::protocol::proto::Message,
    ) -> Result<crate::types::IncomingMessage> {
        let from = web_message
            .key
            .as_ref()
            .and_then(|k| k.remote_jid.clone())
            .unwrap_or_else(|| "unknown".to_string());

        let message_id = web_message
            .key
            .as_ref()
            .and_then(|k| k.id.clone())
            .unwrap_or_else(|| "unknown".to_string());

        let timestamp = web_message
            .message_timestamp
            .map(|ts| std::time::UNIX_EPOCH + std::time::Duration::from_secs(ts))
            .unwrap_or_else(std::time::SystemTime::now);

        // Extract message content
        let content = if let Some(text_msg) = message.conversation {
            crate::types::MessageContent::Text(text_msg)
        } else if let Some(image_msg) = message.image_message {
            crate::types::MessageContent::Image {
                data: image_msg.jpeg_thumbnail.unwrap_or_default(),
                caption: image_msg.caption,
            }
        } else {
            crate::types::MessageContent::Text("Unsupported message type".to_string())
        };

        Ok(crate::types::IncomingMessage {
            from,
            content,
            timestamp,
            message_id: Some(message_id),
        })
    }
}

#[async_trait::async_trait]
impl MessageHandler for ChatMessageHandler {
    async fn handle_message(&self, frame_data: Vec<u8>) -> Result<Option<Event>> {
        tracing::debug!("Processing chat message ({} bytes)", frame_data.len());

        // Try to parse as WebMessageInfo
        if let Ok(mut web_message) =
            crate::protocol::proto::WebMessageInfo::decode(frame_data.as_slice())
        {
            if let Some(message) = web_message.message.take() {
                // Convert to our internal message format
                let incoming_message = self.convert_to_incoming_message(web_message, message)?;
                return Ok(Some(Event::MessageReceived(incoming_message)));
            }
        }

        Ok(None)
    }
}

/// Handler for system messages
pub struct SystemHandler {
    event_sender: broadcast::Sender<Event>,
}

impl SystemHandler {
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        Self { event_sender }
    }
}

#[async_trait::async_trait]
impl MessageHandler for SystemHandler {
    async fn handle_message(&self, frame_data: Vec<u8>) -> Result<Option<Event>> {
        tracing::debug!("Processing system message ({} bytes)", frame_data.len());

        // Handle presence updates, typing indicators, etc.
        if frame_data.starts_with(&[0x02]) {
            tracing::trace!("Presence update received");
            // Could emit presence events here
        }

        Ok(None)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::broadcast;

    #[tokio::test]
    async fn test_frame_processor_creation() {
        let (sender, _) = broadcast::channel(10);
        let processor = WSFrameProcessor::new(sender);

        // Should have default handlers registered
        assert_eq!(processor.message_handlers.len(), 4);
    }

    #[tokio::test]
    async fn test_message_type_detection() {
        let (sender, _) = broadcast::channel(10);
        let processor = WSFrameProcessor::new(sender);

        // Test handshake detection
        let handshake_frame = vec![0x00, 0x00, 0x04, 0x01, 0x02, 0x03, 0x04]; // tag=0x00, length=4, payload
        let msg_type = processor.determine_message_type(&handshake_frame).unwrap();
        assert_eq!(msg_type, WSMessageType::Handshake);

        // Test auth detection
        let auth_frame = vec![0x02, 0x00, 0x04, 0x01, 0x02, 0x03, 0x04]; // tag=0x02, length=4, payload
        let msg_type = processor.determine_message_type(&auth_frame).unwrap();
        assert_eq!(msg_type, WSMessageType::Auth);

        // Test unknown (too short)
        let unknown_frame = vec![0xFF, 0xFE];
        let msg_type = processor.determine_message_type(&unknown_frame).unwrap();
        assert_eq!(msg_type, WSMessageType::Unknown);
    }

    #[tokio::test]
    async fn test_auth_handler() {
        let (sender, _) = broadcast::channel(10);
        let handler = AuthHandler::new(sender);

        // Test QR scan detection with real protocol format
        let mut scan_data = vec![0x42u8; 32];
        scan_data.push(0x01); // Confirmation byte
        let scan_frame = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();
        let result = handler.handle_message(scan_frame).await.unwrap();
        assert!(result.is_some());

        if let Some(Event::LoginSuccess) = result {
            // Expected
        } else {
            panic!("Expected LoginSuccess event");
        }
    }
}
