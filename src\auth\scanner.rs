//! QR code scan detection and authentication message handling

use crate::error::{Result, WhatsAppError};
use crate::types::Event;
use std::sync::Arc;
use tokio::sync::{Mutex, Notify, broadcast};
use tokio::time::{Duration, timeout};

/// Authentication state during QR code scanning
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash)]
pub enum AuthState {
    WaitingForScan,
    ScanDetected,
    AuthenticationComplete,
    AuthenticationFailed(String),
}

/// Authentication events that can trigger state transitions
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum AuthEvent {
    StartScan,
    ScanDetected,
    AuthSuccess,
    AuthFailure(String),
    Reset,
}

/// Authentication state machine with controlled transitions
pub struct AuthStateMachine {
    current_state: AuthState,
    transitions: std::collections::HashMap<(AuthState, AuthEvent), AuthState>,
}

impl AuthStateMachine {
    /// Create a new authentication state machine
    pub fn new() -> Self {
        let mut transitions = std::collections::HashMap::new();

        // Define valid state transitions
        // From WaitingForScan
        transitions.insert(
            (AuthState::WaitingForScan, AuthEvent::StartScan),
            AuthState::WaitingForScan,
        );
        transitions.insert(
            (AuthState::WaitingForScan, AuthEvent::ScanDetected),
            AuthState::ScanDetected,
        );
        transitions.insert(
            (AuthState::WaitingForScan, AuthEvent::AuthSuccess),
            AuthState::AuthenticationComplete,
        );
        transitions.insert(
            (
                AuthState::WaitingForScan,
                AuthEvent::AuthFailure("".to_string()),
            ),
            AuthState::AuthenticationFailed("".to_string()),
        );

        // From ScanDetected
        transitions.insert(
            (AuthState::ScanDetected, AuthEvent::AuthSuccess),
            AuthState::AuthenticationComplete,
        );
        transitions.insert(
            (
                AuthState::ScanDetected,
                AuthEvent::AuthFailure("".to_string()),
            ),
            AuthState::AuthenticationFailed("".to_string()),
        );

        // Reset transitions from any state
        for state in [
            AuthState::WaitingForScan,
            AuthState::ScanDetected,
            AuthState::AuthenticationComplete,
            AuthState::AuthenticationFailed("".to_string()),
        ] {
            transitions.insert((state, AuthEvent::Reset), AuthState::WaitingForScan);
        }

        Self {
            current_state: AuthState::WaitingForScan,
            transitions,
        }
    }

    /// Get the current state
    pub fn current_state(&self) -> &AuthState {
        &self.current_state
    }

    /// Attempt to transition to a new state based on an event
    pub fn transition(&mut self, event: AuthEvent) -> Result<&AuthState> {
        // Handle special cases for events with dynamic data
        let transition_key = match &event {
            AuthEvent::AuthFailure(_reason) => (
                self.current_state.clone(),
                AuthEvent::AuthFailure("".to_string()),
            ),
            AuthEvent::Reset => {
                // Reset is valid from any state, including AuthenticationFailed with any reason
                match &self.current_state {
                    AuthState::AuthenticationFailed(_) => (
                        AuthState::AuthenticationFailed("".to_string()),
                        AuthEvent::Reset,
                    ),
                    _ => (self.current_state.clone(), event.clone()),
                }
            }
            _ => (self.current_state.clone(), event.clone()),
        };

        if let Some(new_state) = self.transitions.get(&transition_key) {
            // Handle special case for AuthFailure with actual reason
            self.current_state = match event {
                AuthEvent::AuthFailure(reason) => AuthState::AuthenticationFailed(reason),
                _ => new_state.clone(),
            };

            tracing::debug!(
                "State transition: {:?} -> {:?}",
                transition_key.0,
                self.current_state
            );

            Ok(&self.current_state)
        } else {
            Err(WhatsAppError::Protocol(format!(
                "Invalid state transition: {:?} with event {:?}",
                self.current_state, event
            )))
        }
    }

    /// Check if a transition is valid without performing it
    pub fn can_transition(&self, event: &AuthEvent) -> bool {
        let transition_key = match event {
            AuthEvent::AuthFailure(_) => (
                self.current_state.clone(),
                AuthEvent::AuthFailure("".to_string()),
            ),
            AuthEvent::Reset => {
                // Reset is valid from any state, including AuthenticationFailed with any reason
                match &self.current_state {
                    AuthState::AuthenticationFailed(_) => (
                        AuthState::AuthenticationFailed("".to_string()),
                        AuthEvent::Reset,
                    ),
                    _ => (self.current_state.clone(), event.clone()),
                }
            }
            _ => (self.current_state.clone(), event.clone()),
        };

        self.transitions.contains_key(&transition_key)
    }

    /// Check if the current state is a terminal state
    pub fn is_terminal(&self) -> bool {
        matches!(
            self.current_state,
            AuthState::AuthenticationComplete | AuthState::AuthenticationFailed(_)
        )
    }

    /// Reset the state machine to initial state
    pub fn reset(&mut self) -> Result<&AuthState> {
        self.transition(AuthEvent::Reset)
    }
}

/// QR code scanner that listens for authentication messages
#[derive(Clone)]
pub struct QRScanner {
    state_machine: Arc<Mutex<AuthStateMachine>>,
    event_sender: broadcast::Sender<Event>,
    scan_data: Arc<Mutex<Option<Vec<u8>>>>,
    state_notify: Arc<Notify>,
}

impl QRScanner {
    /// Create a new QR scanner
    pub fn new(event_sender: broadcast::Sender<Event>) -> Self {
        Self {
            state_machine: Arc::new(Mutex::new(AuthStateMachine::new())),
            event_sender,
            scan_data: Arc::new(Mutex::new(None)),
            state_notify: Arc::new(Notify::new()),
        }
    }

    /// Start listening for QR code scan events
    pub async fn start_scanning(&self) -> Result<()> {
        self.transition_state(AuthEvent::StartScan).await?;
        Ok(())
    }

    /// Process incoming WebSocket frame for authentication messages
    pub async fn process_auth_frame(&self, frame_data: &[u8]) -> Result<bool> {
        // Parse the frame to check if it's an authentication message
        if let Ok(auth_message) = self.parse_auth_message(frame_data) {
            match auth_message {
                AuthMessage::QRScanDetected(scan_data) => {
                    tracing::info!("QR code scan detected!");

                    // Store scan data first
                    {
                        let mut data = self.scan_data.lock().await;
                        *data = Some(scan_data);
                    }

                    // Update state and notify waiters
                    self.transition_state(AuthEvent::ScanDetected).await?;

                    return Ok(true); // Scan detected
                }
                AuthMessage::AuthSuccess => {
                    tracing::info!("Authentication completed successfully");

                    // Update state and notify waiters
                    self.transition_state(AuthEvent::AuthSuccess).await?;

                    // Emit login success event
                    let _ = self.event_sender.send(Event::LoginSuccess);

                    return Ok(true);
                }
                AuthMessage::AuthFailure(reason) => {
                    tracing::warn!("Authentication failed: {}", reason);

                    // Update state and notify waiters
                    self.transition_state(AuthEvent::AuthFailure(reason))
                        .await?;

                    return Ok(true);
                }
                AuthMessage::Other => {
                    // Other authentication-related message, continue processing
                    return Ok(false);
                }
            }
        }

        Ok(false) // Not an authentication message
    }

    /// Wait for QR code scan with timeout using async notification instead of polling
    pub async fn wait_for_scan(&self, timeout_duration: Duration) -> Result<Vec<u8>> {
        timeout(timeout_duration, async {
            loop {
                // Check current state
                let current_state = self.get_state().await;
                match current_state {
                    AuthState::ScanDetected => {
                        // Get scan data
                        let scan_data = self.scan_data.lock().await;
                        if let Some(data) = scan_data.as_ref() {
                            return Ok(data.clone());
                        }
                    }
                    AuthState::AuthenticationComplete => {
                        return Err(WhatsAppError::Protocol(
                            "Authentication completed without scan data".to_string(),
                        ));
                    }
                    AuthState::AuthenticationFailed(reason) => {
                        return Err(WhatsAppError::Authentication(reason));
                    }
                    AuthState::WaitingForScan => {
                        // Continue waiting
                    }
                }

                // Wait for state change notification instead of polling
                self.state_notify.notified().await;
            }
        })
        .await
        .map_err(|_| WhatsAppError::Timeout {
            timeout: timeout_duration,
        })?
    }

    /// Get current authentication state
    pub async fn get_state(&self) -> AuthState {
        let state_machine = self.state_machine.lock().await;
        state_machine.current_state().clone()
    }

    /// Transition state using the state machine and notify all waiters
    async fn transition_state(&self, event: AuthEvent) -> Result<()> {
        {
            let mut state_machine = self.state_machine.lock().await;
            state_machine.transition(event)?;
        }
        // Notify all tasks waiting for state changes
        self.state_notify.notify_waiters();
        Ok(())
    }

    /// Check if a state transition is valid
    pub async fn can_transition(&self, event: &AuthEvent) -> bool {
        let state_machine = self.state_machine.lock().await;
        state_machine.can_transition(event)
    }

    /// Check if the scanner is in a terminal state
    pub async fn is_terminal(&self) -> bool {
        let state_machine = self.state_machine.lock().await;
        state_machine.is_terminal()
    }

    /// Reset the scanner to initial state
    pub async fn reset(&self) -> Result<()> {
        self.transition_state(AuthEvent::Reset).await
    }

    /// Parse authentication message from WebSocket frame
    fn parse_auth_message(&self, frame_data: &[u8]) -> Result<AuthMessage> {
        let parser = AuthMessageParser::new(frame_data)?;
        parser.parse()
    }
}

/// Protocol tag constants for better readability
mod protocol_tags {
    pub const HANDSHAKE: u8 = 0x01;
    pub const QR_SCAN: u8 = 0x02;
    pub const AUTH_RESULT: u8 = 0x03;
    pub const CLIENT_PAYLOAD: u8 = 0x04;
}

/// Authentication result codes
mod auth_result_codes {
    pub const SUCCESS: u8 = 0x01;
    pub const FAILURE: u8 = 0x02;
}

/// Authentication message parser using strategy pattern
struct AuthMessageParser<'a> {
    tag: u8,
    payload: &'a [u8],
}

impl<'a> AuthMessageParser<'a> {
    /// Create a new parser from frame data
    fn new(frame_data: &'a [u8]) -> Result<Self> {
        // WhatsApp Web uses binary frames with specific structure
        // Frame format: [tag][length][payload]
        if frame_data.len() < 3 {
            return Err(WhatsAppError::Protocol("Frame too short".to_string()));
        }

        let tag = frame_data[0];
        let length = u16::from_be_bytes([frame_data[1], frame_data[2]]) as usize;

        if frame_data.len() < 3 + length {
            return Err(WhatsAppError::Protocol("Invalid frame length".to_string()));
        }

        let payload = &frame_data[3..3 + length];

        Ok(Self { tag, payload })
    }

    /// Parse the message using strategy pattern
    fn parse(&self) -> Result<AuthMessage> {
        match self.tag {
            protocol_tags::HANDSHAKE => self.parse_handshake(),
            protocol_tags::QR_SCAN => self.parse_qr_scan(),
            protocol_tags::AUTH_RESULT => self.parse_auth_result(),
            protocol_tags::CLIENT_PAYLOAD => self.parse_client_payload(),
            _ => Ok(AuthMessage::Other),
        }
    }

    /// Parse handshake message
    fn parse_handshake(&self) -> Result<AuthMessage> {
        use prost::Message;

        match crate::protocol::proto::HandshakeMessage::decode(self.payload) {
            Ok(handshake) => {
                if handshake.server_hello.is_some() {
                    tracing::debug!("Received server hello in handshake");
                    Ok(AuthMessage::Other) // Continue handshake process
                } else if handshake.client_finish.is_some() {
                    tracing::info!("Handshake completed successfully");
                    Ok(AuthMessage::AuthSuccess)
                } else {
                    Ok(AuthMessage::Other)
                }
            }
            Err(_) => Ok(AuthMessage::Other),
        }
    }

    /// Parse QR code scan confirmation message
    fn parse_qr_scan(&self) -> Result<AuthMessage> {
        if self.payload.len() < 32 {
            return Ok(AuthMessage::Other);
        }

        // Extract scan data (first 32 bytes are typically the scan confirmation)
        let scan_data = self.payload[0..32].to_vec();

        // Verify this is actually a scan confirmation by checking for specific markers
        if self.payload.len() > 32 && self.payload[32] == 0x01 {
            tracing::info!("QR code scan confirmed by mobile device");
            Ok(AuthMessage::QRScanDetected(scan_data))
        } else {
            Ok(AuthMessage::Other)
        }
    }

    /// Parse authentication result message
    fn parse_auth_result(&self) -> Result<AuthMessage> {
        if self.payload.is_empty() {
            return Ok(AuthMessage::Other);
        }

        match self.payload[0] {
            auth_result_codes::SUCCESS => {
                tracing::info!("Authentication successful");
                Ok(AuthMessage::AuthSuccess)
            }
            auth_result_codes::FAILURE => {
                let reason = if self.payload.len() > 1 {
                    String::from_utf8_lossy(&self.payload[1..]).to_string()
                } else {
                    "Authentication failed".to_string()
                };
                tracing::warn!("Authentication failed: {}", reason);
                Ok(AuthMessage::AuthFailure(reason))
            }
            _ => Ok(AuthMessage::Other),
        }
    }

    /// Parse client payload message
    fn parse_client_payload(&self) -> Result<AuthMessage> {
        use prost::Message;

        match crate::protocol::proto::ClientPayload::decode(self.payload) {
            Ok(client_payload) => {
                if let Some(user_agent) = client_payload.user_agent {
                    if user_agent.platform
                        == Some(crate::protocol::client_payload::user_agent::Platform::Web as i32)
                    {
                        // This indicates successful client authentication
                        return Ok(AuthMessage::AuthSuccess);
                    }
                }
                Ok(AuthMessage::Other)
            }
            Err(_) => Ok(AuthMessage::Other),
        }
    }
}

/// Authentication message types
#[derive(Debug, Clone)]
enum AuthMessage {
    QRScanDetected(Vec<u8>),
    AuthSuccess,
    AuthFailure(String),
    Other,
}

/// Production QR code authentication handler
pub struct ProductionAuthHandler {
    scanner: QRScanner,
    connection: Arc<Mutex<crate::client::connection::Connection>>,
}

impl ProductionAuthHandler {
    /// Create a new production authentication handler
    pub fn new(
        event_sender: broadcast::Sender<Event>,
        connection: Arc<Mutex<crate::client::connection::Connection>>,
    ) -> Self {
        Self {
            scanner: QRScanner::new(event_sender),
            connection,
        }
    }

    /// Perform real QR code authentication
    pub async fn authenticate(&self, qr_data: &str) -> Result<Vec<u8>> {
        tracing::info!("Starting production QR code authentication");

        self.scanner.start_scanning().await?;
        self.initiate_handshake(qr_data).await?;

        let listener_handle = self.start_auth_listener().await;
        let result = self.wait_for_authentication().await;

        listener_handle.abort();
        result
    }

    /// Initiate the QR code handshake with WhatsApp servers
    async fn initiate_handshake(&self, qr_data: &str) -> Result<()> {
        // In the real WhatsApp Web protocol, we don't send a handshake message immediately
        // The QR code contains the necessary information, and authentication happens
        // when the mobile app scans it and communicates with WhatsApp servers
        tracing::debug!("QR code ready for scanning: {}", qr_data);

        // Just return success - the real handshake will happen when QR is scanned
        Ok(())
    }

    /// Start listening for authentication messages in a separate task
    async fn start_auth_listener(&self) -> tokio::task::JoinHandle<()> {
        let scanner = self.scanner.clone();
        let connection = self.connection.clone();

        tokio::spawn(async move {
            Self::auth_message_listener(scanner, connection).await;
        })
    }

    /// Authentication message listener loop
    async fn auth_message_listener(
        scanner: QRScanner,
        connection: Arc<Mutex<crate::client::connection::Connection>>,
    ) {
        let timeout_duration = Duration::from_secs(120); // 2 minutes for QR scan
        let start_time = std::time::Instant::now();

        while start_time.elapsed() < timeout_duration {
            let frame_result = Self::receive_frame_with_timeout(&connection).await;

            match frame_result {
                Ok(Ok(frame_data)) => {
                    if Self::process_auth_frame_and_check_completion(&scanner, &frame_data).await {
                        break;
                    }
                }
                Ok(Err(e)) => {
                    tracing::debug!("Frame receive error during auth: {}", e);
                    if Self::is_fatal_connection_error(&e) {
                        break;
                    }
                }
                Err(_) => {
                    // Timeout receiving frame - continue listening
                    continue;
                }
            }
        }
    }

    /// Receive a frame from connection with timeout
    async fn receive_frame_with_timeout(
        connection: &Arc<Mutex<crate::client::connection::Connection>>,
    ) -> std::result::Result<Result<Vec<u8>>, tokio::time::error::Elapsed> {
        tokio::time::timeout(Duration::from_secs(10), async {
            let mut conn = connection.lock().await;
            conn.receive_frame().await
        })
        .await
    }

    /// Process authentication frame and check if authentication is complete
    async fn process_auth_frame_and_check_completion(
        scanner: &QRScanner,
        frame_data: &[u8],
    ) -> bool {
        match scanner.process_auth_frame(frame_data).await {
            Ok(true) => {
                // Authentication message processed, check if we're done
                let state = scanner.get_state().await;
                matches!(
                    state,
                    AuthState::AuthenticationComplete | AuthState::AuthenticationFailed(_)
                )
            }
            Ok(false) => {
                // Not an auth message, continue listening
                false
            }
            Err(e) => {
                tracing::error!("Error processing auth frame: {}", e);
                true // Stop listening on processing errors
            }
        }
    }

    /// Check if the error is fatal and should stop the listener
    fn is_fatal_connection_error(error: &WhatsAppError) -> bool {
        matches!(
            error,
            WhatsAppError::Connection(_) | WhatsAppError::NotConnected
        )
    }

    /// Wait for authentication to complete and return the result
    async fn wait_for_authentication(&self) -> Result<Vec<u8>> {
        let scan_result = timeout(
            Duration::from_secs(120), // 2 minutes timeout for real QR scanning
            self.scanner.wait_for_scan(Duration::from_secs(120)),
        )
        .await;

        match scan_result {
            Ok(Ok(scan_data)) => {
                tracing::info!("QR code scan completed successfully");
                Ok(scan_data)
            }
            Ok(Err(e)) => {
                tracing::debug!("QR code scan failed: {}", e);
                Err(e)
            }
            Err(_) => {
                tracing::debug!("QR code scan timed out after 2 minutes");
                Err(WhatsAppError::Timeout {
                    timeout: Duration::from_secs(120),
                })
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::broadcast;

    #[tokio::test]
    async fn test_qr_scanner_creation() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::WaitingForScan);
    }

    #[tokio::test]
    async fn test_qr_scanner_state_transitions() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        // Start scanning
        scanner.start_scanning().await.unwrap();
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::WaitingForScan);

        // Simulate QR scan detection with real protocol format
        // Format: [tag=0x02][length=0x00,0x21][32 bytes scan data][0x01 confirmation]
        let mut scan_data = vec![0x42u8; 32]; // 32 bytes of scan data
        scan_data.push(0x01); // Confirmation byte
        let frame_data = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();

        let result = scanner.process_auth_frame(&frame_data).await.unwrap();
        assert!(result); // Should return true for processed auth message

        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::ScanDetected);
    }

    #[tokio::test]
    async fn test_auth_message_parsing() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        // Test QR scan detection pattern with real protocol format
        let mut scan_data = vec![0x42u8; 32];
        scan_data.push(0x01); // Confirmation byte
        let scan_frame = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();
        let result = scanner.process_auth_frame(&scan_frame).await.unwrap();
        assert!(result);

        // Test auth success pattern
        let success_frame = vec![0x03, 0x00, 0x01, 0x01]; // tag=0x03, length=1, payload=0x01 (success)
        let result = scanner.process_auth_frame(&success_frame).await.unwrap();
        assert!(result);

        // Test non-auth frame (too short)
        let other_frame = vec![0xFF, 0xFE];
        let result = scanner.process_auth_frame(&other_frame).await.unwrap();
        assert!(!result);
    }

    #[tokio::test]
    async fn test_async_notification_instead_of_polling() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender.clone());

        scanner.start_scanning().await.unwrap();

        // Spawn a task that will trigger scan detection after a short delay
        let scanner_clone = scanner.clone();
        tokio::spawn(async move {
            tokio::time::sleep(Duration::from_millis(50)).await;

            // Simulate QR scan detection
            let mut scan_data = vec![0x42u8; 32];
            scan_data.push(0x01);
            let frame_data = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();

            scanner_clone.process_auth_frame(&frame_data).await.unwrap();
        });

        // This should complete quickly due to async notification, not polling
        let start = std::time::Instant::now();
        let result = scanner.wait_for_scan(Duration::from_secs(1)).await;
        let elapsed = start.elapsed();

        assert!(result.is_ok());
        // Should complete much faster than 1 second due to notification
        assert!(elapsed < Duration::from_millis(200));
    }

    #[tokio::test]
    async fn test_production_auth_handler_error_classification() {
        // Test fatal connection error detection without creating complex error instances
        let not_connected_error = WhatsAppError::NotConnected;
        assert!(ProductionAuthHandler::is_fatal_connection_error(
            &not_connected_error
        ));

        let timeout_error = WhatsAppError::Timeout {
            timeout: Duration::from_secs(1),
        };
        assert!(!ProductionAuthHandler::is_fatal_connection_error(
            &timeout_error
        ));

        let protocol_error = WhatsAppError::Protocol("Test error".to_string());
        assert!(!ProductionAuthHandler::is_fatal_connection_error(
            &protocol_error
        ));
    }

    #[tokio::test]
    async fn test_auth_frame_processing_completion_check() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        // Test with scan detected state - should continue listening (return false)
        let mut scan_data = vec![0x42u8; 32];
        scan_data.push(0x01);
        let frame_data = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();

        let should_stop =
            ProductionAuthHandler::process_auth_frame_and_check_completion(&scanner, &frame_data)
                .await;

        // Should return false (continue listening) when scan is detected
        assert!(!should_stop);

        // Verify state is actually ScanDetected
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::ScanDetected);

        // Test with authentication success - should stop listening (return true)
        let success_frame = vec![0x03, 0x00, 0x01, 0x01]; // tag=0x03, length=1, payload=0x01 (success)
        let should_stop = ProductionAuthHandler::process_auth_frame_and_check_completion(
            &scanner,
            &success_frame,
        )
        .await;

        // Should return true (stop listening) when authentication is complete
        assert!(should_stop);

        // Verify state is AuthenticationComplete
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::AuthenticationComplete);
    }

    #[test]
    fn test_auth_message_parser_creation() {
        // Test valid frame creation
        let frame_data = vec![0x02, 0x00, 0x01, 0x42]; // tag=0x02, length=1, payload=[0x42]
        let parser = AuthMessageParser::new(&frame_data);
        assert!(parser.is_ok());

        // Test frame too short
        let short_frame = vec![0x02, 0x00]; // Only 2 bytes
        let parser = AuthMessageParser::new(&short_frame);
        assert!(parser.is_err());

        // Test invalid length
        let invalid_frame = vec![0x02, 0x00, 0x10]; // Claims 16 bytes payload but has none
        let parser = AuthMessageParser::new(&invalid_frame);
        assert!(parser.is_err());
    }

    #[test]
    fn test_auth_message_parser_individual_strategies() {
        // Test QR scan parsing in isolation
        let mut scan_data = vec![0x42u8; 32];
        scan_data.push(0x01); // Confirmation marker
        let frame_data = [&[0x02, 0x00, 0x21], scan_data.as_slice()].concat();

        let parser = AuthMessageParser::new(&frame_data).unwrap();
        let result = parser.parse().unwrap();

        match result {
            AuthMessage::QRScanDetected(data) => {
                assert_eq!(data.len(), 32);
                assert_eq!(data[0], 0x42);
            }
            _ => panic!("Expected QRScanDetected"),
        }

        // Test authentication success parsing in isolation
        let success_frame = vec![0x03, 0x00, 0x01, 0x01]; // tag=0x03, length=1, payload=0x01 (success)
        let parser = AuthMessageParser::new(&success_frame).unwrap();
        let result = parser.parse().unwrap();

        match result {
            AuthMessage::AuthSuccess => {} // Expected
            _ => panic!("Expected AuthSuccess"),
        }

        // Test authentication failure parsing in isolation
        let failure_frame = vec![0x03, 0x00, 0x05, 0x02, b'f', b'a', b'i', b'l']; // tag=0x03, length=5, payload=[0x02, "fail"]
        let parser = AuthMessageParser::new(&failure_frame).unwrap();
        let result = parser.parse().unwrap();

        match result {
            AuthMessage::AuthFailure(reason) => {
                assert_eq!(reason, "fail");
            }
            _ => panic!("Expected AuthFailure"),
        }

        // Test unknown tag
        let unknown_frame = vec![0xFF, 0x00, 0x01, 0x42]; // Unknown tag
        let parser = AuthMessageParser::new(&unknown_frame).unwrap();
        let result = parser.parse().unwrap();

        match result {
            AuthMessage::Other => {} // Expected
            _ => panic!("Expected Other"),
        }
    }

    #[test]
    fn test_protocol_constants_usage() {
        // Test that our constants match the expected values
        assert_eq!(protocol_tags::HANDSHAKE, 0x01);
        assert_eq!(protocol_tags::QR_SCAN, 0x02);
        assert_eq!(protocol_tags::AUTH_RESULT, 0x03);
        assert_eq!(protocol_tags::CLIENT_PAYLOAD, 0x04);

        assert_eq!(auth_result_codes::SUCCESS, 0x01);
        assert_eq!(auth_result_codes::FAILURE, 0x02);
    }

    #[test]
    fn test_auth_state_machine_creation() {
        let state_machine = AuthStateMachine::new();

        // Should start in WaitingForScan state
        assert_eq!(*state_machine.current_state(), AuthState::WaitingForScan);
        assert!(!state_machine.is_terminal());
    }

    #[test]
    fn test_auth_state_machine_valid_transitions() {
        let mut state_machine = AuthStateMachine::new();

        // Test StartScan transition (stays in WaitingForScan)
        let result = state_machine.transition(AuthEvent::StartScan);
        assert!(result.is_ok());
        assert_eq!(*state_machine.current_state(), AuthState::WaitingForScan);

        // Test ScanDetected transition
        let result = state_machine.transition(AuthEvent::ScanDetected);
        assert!(result.is_ok());
        assert_eq!(*state_machine.current_state(), AuthState::ScanDetected);
        assert!(!state_machine.is_terminal());

        // Test AuthSuccess transition from ScanDetected
        let result = state_machine.transition(AuthEvent::AuthSuccess);
        assert!(result.is_ok());
        assert_eq!(
            *state_machine.current_state(),
            AuthState::AuthenticationComplete
        );
        assert!(state_machine.is_terminal());
    }

    #[test]
    fn test_auth_state_machine_failure_transitions() {
        let mut state_machine = AuthStateMachine::new();

        // Test direct failure from WaitingForScan
        let result =
            state_machine.transition(AuthEvent::AuthFailure("Connection lost".to_string()));
        assert!(result.is_ok());
        assert_eq!(
            *state_machine.current_state(),
            AuthState::AuthenticationFailed("Connection lost".to_string())
        );
        assert!(state_machine.is_terminal());

        // Test reset from failed state
        let result = state_machine.reset();
        assert!(result.is_ok());
        assert_eq!(*state_machine.current_state(), AuthState::WaitingForScan);
        assert!(!state_machine.is_terminal());
    }

    #[test]
    fn test_auth_state_machine_invalid_transitions() {
        let mut state_machine = AuthStateMachine::new();

        // Move to terminal state
        state_machine.transition(AuthEvent::AuthSuccess).unwrap();
        assert_eq!(
            *state_machine.current_state(),
            AuthState::AuthenticationComplete
        );

        // Try invalid transition from terminal state (should fail)
        let result = state_machine.transition(AuthEvent::ScanDetected);
        assert!(result.is_err());

        // State should remain unchanged after failed transition
        assert_eq!(
            *state_machine.current_state(),
            AuthState::AuthenticationComplete
        );
    }

    #[test]
    fn test_auth_state_machine_transition_validation() {
        let state_machine = AuthStateMachine::new();

        // Test valid transitions
        assert!(state_machine.can_transition(&AuthEvent::StartScan));
        assert!(state_machine.can_transition(&AuthEvent::ScanDetected));
        assert!(state_machine.can_transition(&AuthEvent::AuthSuccess));
        assert!(state_machine.can_transition(&AuthEvent::AuthFailure("test".to_string())));
        assert!(state_machine.can_transition(&AuthEvent::Reset));
    }

    #[tokio::test]
    async fn test_qr_scanner_state_machine_integration() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        // Test initial state
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::WaitingForScan);
        assert!(!scanner.is_terminal().await);

        // Test valid transition
        assert!(scanner.can_transition(&AuthEvent::ScanDetected).await);
        scanner
            .transition_state(AuthEvent::ScanDetected)
            .await
            .unwrap();

        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::ScanDetected);

        // Test terminal state
        scanner
            .transition_state(AuthEvent::AuthSuccess)
            .await
            .unwrap();
        assert!(scanner.is_terminal().await);

        // Test reset
        scanner.reset().await.unwrap();
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::WaitingForScan);
        assert!(!scanner.is_terminal().await);
    }

    #[tokio::test]
    async fn test_qr_scanner_invalid_state_transitions() {
        let (sender, _) = broadcast::channel(10);
        let scanner = QRScanner::new(sender);

        // Move to terminal state
        scanner
            .transition_state(AuthEvent::AuthSuccess)
            .await
            .unwrap();
        assert!(scanner.is_terminal().await);

        // Try invalid transition - should fail
        let result = scanner.transition_state(AuthEvent::ScanDetected).await;
        assert!(result.is_err());

        // State should remain unchanged
        let state = scanner.get_state().await;
        assert_eq!(state, AuthState::AuthenticationComplete);
    }
}
