//! Error types for the WhatsApp client

use std::time::Duration;
use thiserror::Error;

/// Main error type for WhatsApp operations
#[derive(Debug, Error)]
pub enum WhatsAppError {
    #[error("Connection error: {0}")]
    Connection(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("Authentication failed: {0}")]
    Authentication(String),

    #[error("Protocol error: {0}")]
    Protocol(String),

    #[error("Encryption error: {0}")]
    Encryption(String),

    #[error("Serialization error: {0}")]
    Serialization(#[from] prost::DecodeError),

    #[error("Encoding error: {0}")]
    Encoding(#[from] prost::EncodeError),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Session error: {0}")]
    Session(String),

    #[error("QR code generation failed: {0}")]
    QrCode(String),

    #[error("Timeout occurred after {timeout:?}")]
    Timeout { timeout: std::time::Duration },

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Client not connected")]
    NotConnected,

    #[error("Client already connected")]
    AlreadyConnected,

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Operation '{operation}' failed: {context}")]
    Operation {
        operation: String,
        context: String,
        #[source]
        source: Box<WhatsAppError>,
    },

    #[error("Rate limited: {message}. Retry after {retry_after:?}")]
    RateLimited {
        message: String,
        retry_after: Option<Duration>,
    },

    #[error("Network error: {0}")]
    Network(String),

    #[error("Transient error: {message} (attempt {attempt}/{max_attempts})")]
    Transient {
        message: String,
        attempt: u32,
        max_attempts: u32,
        #[source]
        source: Option<Box<WhatsAppError>>,
    },

    #[error("Generic error: {0}")]
    Generic(String),
}

/// Convenience Result type
pub type Result<T> = std::result::Result<T, WhatsAppError>;
impl WhatsAppError {
    /// Create a new operation error with context
    pub fn operation(
        operation: impl Into<String>,
        context: impl Into<String>,
        source: WhatsAppError,
    ) -> Self {
        Self::Operation {
            operation: operation.into(),
            context: context.into(),
            source: Box::new(source),
        }
    }

    /// Create a transient error that can be retried
    pub fn transient(
        message: impl Into<String>,
        attempt: u32,
        max_attempts: u32,
        source: Option<WhatsAppError>,
    ) -> Self {
        Self::Transient {
            message: message.into(),
            attempt,
            max_attempts,
            source: source.map(Box::new),
        }
    }

    /// Create a rate limited error with retry information
    pub fn rate_limited(message: impl Into<String>, retry_after: Option<Duration>) -> Self {
        Self::RateLimited {
            message: message.into(),
            retry_after,
        }
    }

    /// Check if this error is related to authentication
    pub fn is_auth_error(&self) -> bool {
        matches!(self, WhatsAppError::Authentication(_))
    }

    /// Check if this error is related to connection issues
    pub fn is_connection_error(&self) -> bool {
        matches!(
            self,
            WhatsAppError::Connection(_)
                | WhatsAppError::NotConnected
                | WhatsAppError::AlreadyConnected
                | WhatsAppError::Network(_)
        )
    }

    /// Check if this error is recoverable (can retry the operation)
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            WhatsAppError::Connection(_)
                | WhatsAppError::Timeout { .. }
                | WhatsAppError::NotConnected
                | WhatsAppError::Network(_)
                | WhatsAppError::Transient { .. }
                | WhatsAppError::RateLimited { .. }
        )
    }

    /// Check if this error is transient and should be retried
    pub fn is_transient(&self) -> bool {
        matches!(
            self,
            WhatsAppError::Transient { .. }
                | WhatsAppError::Timeout { .. }
                | WhatsAppError::Network(_)
        )
    }

    /// Check if this error indicates rate limiting
    pub fn is_rate_limited(&self) -> bool {
        matches!(self, WhatsAppError::RateLimited { .. })
    }

    /// Get retry delay for rate limited errors
    pub fn retry_delay(&self) -> Option<Duration> {
        match self {
            WhatsAppError::RateLimited { retry_after, .. } => *retry_after,
            WhatsAppError::Transient { attempt, .. } => {
                // Exponential backoff: 2^attempt seconds, max 60 seconds
                let delay_secs = (2_u64.pow(*attempt)).min(60);
                Some(Duration::from_secs(delay_secs))
            }
            _ => None,
        }
    }

    /// Clone this error if possible (some error types cannot be cloned)
    pub fn try_clone(&self) -> Option<Self> {
        match self {
            WhatsAppError::Authentication(msg) => Some(WhatsAppError::Authentication(msg.clone())),
            WhatsAppError::Protocol(msg) => Some(WhatsAppError::Protocol(msg.clone())),
            WhatsAppError::Encryption(msg) => Some(WhatsAppError::Encryption(msg.clone())),
            WhatsAppError::Session(msg) => Some(WhatsAppError::Session(msg.clone())),
            WhatsAppError::QrCode(msg) => Some(WhatsAppError::QrCode(msg.clone())),
            WhatsAppError::Timeout { timeout } => {
                Some(WhatsAppError::Timeout { timeout: *timeout })
            }
            WhatsAppError::InvalidConfig(msg) => Some(WhatsAppError::InvalidConfig(msg.clone())),
            WhatsAppError::NotConnected => Some(WhatsAppError::NotConnected),
            WhatsAppError::AlreadyConnected => Some(WhatsAppError::AlreadyConnected),
            WhatsAppError::InvalidInput(msg) => Some(WhatsAppError::InvalidInput(msg.clone())),
            WhatsAppError::RateLimited {
                message,
                retry_after,
            } => Some(WhatsAppError::RateLimited {
                message: message.clone(),
                retry_after: *retry_after,
            }),
            WhatsAppError::Network(msg) => Some(WhatsAppError::Network(msg.clone())),
            WhatsAppError::Transient {
                message,
                attempt,
                max_attempts,
                source,
            } => Some(WhatsAppError::Transient {
                message: message.clone(),
                attempt: *attempt,
                max_attempts: *max_attempts,
                source: source.as_ref().and_then(|s| s.try_clone().map(Box::new)),
            }),
            WhatsAppError::Generic(msg) => Some(WhatsAppError::Generic(msg.clone())),
            WhatsAppError::Operation {
                operation,
                context,
                source,
            } => source
                .try_clone()
                .map(|cloned_source| WhatsAppError::Operation {
                    operation: operation.clone(),
                    context: context.clone(),
                    source: Box::new(cloned_source),
                }),
            // These types cannot be cloned
            WhatsAppError::Connection(_)
            | WhatsAppError::Serialization(_)
            | WhatsAppError::Encoding(_)
            | WhatsAppError::Io(_) => None,
        }
    }

    /// Get error severity level for logging
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            WhatsAppError::Authentication(_) => ErrorSeverity::High,
            WhatsAppError::Encryption(_) => ErrorSeverity::High,
            WhatsAppError::InvalidConfig(_) => ErrorSeverity::High,
            WhatsAppError::Connection(_) => ErrorSeverity::Medium,
            WhatsAppError::Network(_) => ErrorSeverity::Medium,
            WhatsAppError::NotConnected => ErrorSeverity::Medium,
            WhatsAppError::Timeout { .. } => ErrorSeverity::Medium,
            WhatsAppError::RateLimited { .. } => ErrorSeverity::Medium,
            WhatsAppError::Transient { .. } => ErrorSeverity::Low,
            WhatsAppError::InvalidInput(_) => ErrorSeverity::Low,
            _ => ErrorSeverity::Medium,
        }
    }

    /// Get user-friendly error message
    pub fn user_message(&self) -> String {
        match self {
            WhatsAppError::Authentication(_) => {
                "Authentication failed. Please try logging in again.".to_string()
            }
            WhatsAppError::NotConnected => {
                "Not connected to WhatsApp. Please check your internet connection.".to_string()
            }
            WhatsAppError::Timeout { .. } => {
                "Operation timed out. Please check your internet connection and try again."
                    .to_string()
            }
            WhatsAppError::RateLimited { retry_after, .. } => {
                if let Some(delay) = retry_after {
                    format!("Rate limited. Please wait {:?} before trying again.", delay)
                } else {
                    "Rate limited. Please wait before trying again.".to_string()
                }
            }
            WhatsAppError::InvalidInput(msg) => format!("Invalid input: {}", msg),
            _ => "An error occurred. Please try again.".to_string(),
        }
    }

    /// Log this error with appropriate level
    pub fn log(&self) {
        match self.severity() {
            ErrorSeverity::High => tracing::error!("{}", self),
            ErrorSeverity::Medium => tracing::warn!("{}", self),
            ErrorSeverity::Low => tracing::debug!("{}", self),
        }
    }

    /// Log this error with context
    pub fn log_with_context(&self, context: &str) {
        match self.severity() {
            ErrorSeverity::High => tracing::error!("{}: {}", context, self),
            ErrorSeverity::Medium => tracing::warn!("{}: {}", context, self),
            ErrorSeverity::Low => tracing::debug!("{}: {}", context, self),
        }
    }
}

/// Error severity levels for logging and handling
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
}
